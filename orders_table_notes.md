# Orders Table Schema and Triggers

## Schema

The orders table schema includes additional fields not documented in the original code:

- `payment_status`
- `address_id`
- `razorpay_payment_id`
- `payment_auth_date`
- `payment_capture_date`
- `payment_failure_date`
- `payment_error`
- `payment_error_code`

## Triggers

The orders table has triggers that enforce referential integrity with other tables:

- `RI_ConstraintTrigger_a_18073` - This trigger enforces the foreign key relationship between `order_items.order_id` and `orders.id`. This trigger should NOT be dropped as it maintains database integrity.

## Solution

To fix the 400 Bad Request error when updating orders:

1. We've updated the TypeScript interface to include all the additional fields
2. We've modified the update functions to preserve all existing fields when updating an order
3. We're now removing nested objects (customers, order_items) before sending the update to Supabase

This approach ensures that all required fields are included in the update operation, while still allowing us to modify the status of an order.

## Important Note

Do not attempt to drop the foreign key constraint triggers as they are essential for database integrity. If you need to modify the database schema, consider using migrations or altering the table structure properly.
