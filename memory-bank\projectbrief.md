# Project Brief

Dhanam Supermarket is enhancing its customer ordering experience by implementing a **WhatsApp-based ordering system** integrated with an **admin dashboard** built using **React (Vite) and Supabase**. Customers can seamlessly browse products, place orders, and receive real-time updates via WhatsApp without needing a separate app. The admin panel features a **Kanban board for efficient order management**, real-time status updates, and customer tracking. **Automated WhatsApp notifications** ensure smooth communication for order confirmations, tracking, and promotions. This solution simplifies operations, reduces manual workload, and improves customer engagement, making the ordering process faster and more convenient.
**Project Name:** Dhanam Store

**Project Description:** A web application for managing orders, returns, and customers.

**Core Requirements and Goals:**

- Implement a user authentication system.
- Provide a dashboard for managing orders, returns, and customers.
- Use a modern UI framework (React) and component library (Radix UI).
- Implement a responsive layout that works on different screen sizes.
- Use TypeScript for type safety and maintainability.

# Schema

### 1. **Users Table (`users`)**

Stores admin users (Super Admin and Admin).

```sql
sql
CopyEdit
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    phone TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,  -- Hashed password
    role TEXT CHECK (role IN ('Super Admin', 'Admin')) NOT NULL DEFAULT 'Admin',
    created_at TIMESTAMP DEFAULT NOW()
);

```

---

### 2. **Customers Table (`customers`)**

Stores customers who interact via WhatsApp.

```sql
sql
CopyEdit
CREATE TABLE customers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    whatsapp_number TEXT UNIQUE NOT NULL,
    email TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

```

---

### 3. **Orders Table (`orders`)**

Stores order details and tracks status, including cancellation reason.

```sql
sql
CopyEdit
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
    total_amount DECIMAL(10,2) NOT NULL,
    status TEXT CHECK (status IN ('New', 'Processing', 'Out for Delivery', 'Delivered', 'Cancelled')) DEFAULT 'New',
    payment_method TEXT CHECK (payment_method IN ('COD', 'ONLINE')) NOT NULL,
    cancellation_reason TEXT, -- Stores cancellation reason if applicable
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

```

---

### 4. **Order Items Table (`order_items`)**

Stores products ordered, with cancellation reason for individual items if applicable.

```sql
sql
CopyEdit
CREATE TABLE order_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    product_name TEXT NOT NULL,
    product_description TEXT,
    product_image TEXT,
    quantity INT NOT NULL CHECK (quantity > 0),
    price DECIMAL(10,2) NOT NULL,
    cancellation_reason TEXT -- Added column for item-specific cancellation reason
);

```

---

### 5. **Addresses Table (`addresses`)**

Stores delivery addresses for customers.

```sql
sql
CopyEdit
CREATE TABLE addresses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
    address TEXT NOT NULL,
    pincode TEXT NOT NULL,
    is_default BOOLEAN DEFAULT FALSE
);

```

---

### 6. **Delivery Areas Table (`delivery_areas`)**

Stores serviceable delivery locations (Removed delivery_charge).

```sql
sql
CopyEdit
CREATE TABLE delivery_areas (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    pincode TEXT UNIQUE NOT NULL
);

```

---

### 7. **Payments Table (`payments`)**

Stores payment details with cancellation/refund reason if applicable.

```sql
sql
CopyEdit
CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    payment_method TEXT CHECK (payment_method IN ('COD', 'ONLINE')) NOT NULL,
    payment_status TEXT CHECK (payment_status IN ('Pending', 'Completed', 'Failed', 'Refunded')) DEFAULT 'Pending',
    transaction_id TEXT UNIQUE,
    cancellation_reason TEXT, -- Added for cancellations/refunds
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

```

---

### 8. **Return Products Table (`return_products`)** *(New)*

Tracks product return requests from customers.

```sql
sql
CopyEdit
CREATE TABLE return_products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    product_name TEXT NOT NULL,
    quantity INT NOT NULL CHECK (quantity > 0),
    return_reason TEXT NOT NULL,
    status TEXT CHECK (status IN ('Requested', 'Approved', 'Rejected')) DEFAULT 'Requested',
    requested_at TIMESTAMP DEFAULT NOW(),
    processed_at TIMESTAMP,
    processed_by UUID REFERENCES users(id)
);
### `categories` Table

```sql
sql
CopyEdit
CREATE TABLE categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL UNIQUE,
    description TEXT,
        image_url TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

```

### `products` Table

```sql
sql
CopyEdit
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    name TEXT NOT NULL,
    description TEXT,
    image_url TEXT,
    price NUMERIC(10, 2) NOT NULL CHECK (price >= 0),
    stock_quantity INTEGER DEFAULT 0 CHECK (stock_quantity >= 0),
    is_available BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);


``