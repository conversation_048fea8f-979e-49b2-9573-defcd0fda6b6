-- SQL script to check for JSON validation triggers on the orders table

-- List all triggers on the orders table
SELECT 
    tgname AS trigger_name,
    pg_get_triggerdef(t.oid) AS trigger_definition
FROM 
    pg_trigger t
WHERE 
    tgrelid = 'orders'::regclass;

-- Check for any functions that might be validating JSON
SELECT 
    p.proname AS function_name,
    pg_get_functiondef(p.oid) AS function_definition
FROM 
    pg_proc p
    JOIN pg_trigger t ON p.oid = t.tgfoid
WHERE 
    t.tgrelid = 'orders'::regclass
    AND pg_get_functiondef(p.oid) LIKE '%json%';

-- Check table constraints that might involve JSON validation
SELECT
    conname AS constraint_name,
    pg_get_constraintdef(oid) AS constraint_definition
FROM
    pg_constraint
WHERE
    conrelid = 'orders'::regclass
    AND pg_get_constraintdef(oid) LIKE '%json%';
