# Instructions to Remove Triggers from Orders Table

Follow these steps to remove all triggers from the orders table in your Supabase project:

1. Log in to your Supabase dashboard at https://app.supabase.io/
2. Select your project: "phmbblwthsregggkwbfo"
3. Navigate to the SQL Editor in the left sidebar
4. Create a new query
5. Copy and paste the following SQL code:

```sql
-- SQL script to drop all triggers on the orders table

-- First, get a list of all triggers on the orders table
DO $$
DECLARE
    trigger_record RECORD;
BEGIN
    FOR trigger_record IN 
        SELECT tgname AS trigger_name
        FROM pg_trigger
        WHERE tgrelid = 'orders'::regclass
    LOOP
        EXECUTE format('DROP TRIGGER IF EXISTS %I ON orders', trigger_record.trigger_name);
        RAISE NOTICE 'Dropped trigger: %', trigger_record.trigger_name;
    END LOOP;
END $$;

-- Verify no triggers remain
SELECT tgname FROM pg_trigger WHERE tgrelid = 'orders'::regclass;
```

6. Click "Run" to execute the SQL
7. Check the results to confirm all triggers have been removed
8. Test your application again to see if the issue with updating order status has been resolved

## Alternative: Using the Supabase API

If you prefer to use the Supabase API directly, you can also execute this SQL using the REST API:

```javascript
const { data, error } = await supabase.rpc('exec_sql', {
  sql_query: `
    DO $$
    DECLARE
        trigger_record RECORD;
    BEGIN
        FOR trigger_record IN 
            SELECT tgname AS trigger_name
            FROM pg_trigger
            WHERE tgrelid = 'orders'::regclass
        LOOP
            EXECUTE format('DROP TRIGGER IF EXISTS %I ON orders', trigger_record.trigger_name);
            RAISE NOTICE 'Dropped trigger: %', trigger_record.trigger_name;
        END LOOP;
    END $$;
  `
});
```

Note: You'll need to have the `exec_sql` function set up in your database for this to work.
