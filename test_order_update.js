// Test script to verify order updates work after removing triggers
import { supabase } from './src/lib/supabase.config';

// Replace with an actual order ID from your database
const TEST_ORDER_ID = '56447b10-0bc6-4edb-86d2-7e514fc6cc38';

async function testOrderUpdate() {
  console.log('Testing order update...');
  
  // First, get the current order status
  const { data: orderBefore, error: fetchError } = await supabase
    .from('orders')
    .select('status, updated_at')
    .eq('id', TEST_ORDER_ID)
    .single();
    
  if (fetchError) {
    console.error('Error fetching order:', fetchError);
    return;
  }
  
  console.log('Current order status:', orderBefore.status);
  
  // Update the order with the same status (just to test the update operation)
  const { data: updateResult, error: updateError } = await supabase
    .from('orders')
    .update({ 
      status: orderBefore.status,
      updated_at: new Date().toISOString()
    })
    .eq('id', TEST_ORDER_ID);
    
  if (updateError) {
    console.error('Error updating order:', updateError);
    return;
  }
  
  console.log('Order update successful!');
  
  // Verify the update
  const { data: orderAfter, error: verifyError } = await supabase
    .from('orders')
    .select('status, updated_at')
    .eq('id', TEST_ORDER_ID)
    .single();
    
  if (verifyError) {
    console.error('Error verifying order update:', verifyError);
    return;
  }
  
  console.log('Order after update:', orderAfter);
  console.log('Update test completed successfully!');
}

testOrderUpdate();
