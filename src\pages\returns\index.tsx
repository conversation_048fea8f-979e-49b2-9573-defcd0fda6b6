import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase.config';
import { format, formatDistanceToNow } from 'date-fns';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Search, Plus, Check, X, RotateCcw } from 'lucide-react';
import { ReturnRequest, ReturnStatus, OrderItem } from '@/types';

const statusColors = {
  Requested: 'bg-yellow-500',
  Approved: 'bg-blue-500',
  Rejected: 'bg-red-500',
  Returned: 'bg-green-500',
};

const statusNames = {
  Requested: 'Requested',
  Approved: 'Approved',
  Rejected: 'Rejected',
  Returned: 'Returned',
};

export function Returns() {
  const [returns, setReturns] = useState<ReturnRequest[]>([]);
  const [_loading, setLoading] = useState(true);

  useEffect(() => {
    fetchReturns();
    setupRealtime();
    return () => {
      supabase.removeAllChannels();
    };
  }, []);
  type OrderOption = {
    id: string;
  };

  const [orderOptions, setOrderOptions] = useState<OrderOption[]>([]);

  useEffect(() => {
    async function fetchOrders() {
      const { data, error } = await supabase
        .from('orders')
        .select('id')
        .order('created_at', { ascending: false });

      if (!error && data) setOrderOptions(data);
    }

    fetchOrders();
  }, []);
  const fetchReturns = async () => {
    setLoading(true);

    const { data, error } = await supabase
      .from('return_products')
      .select(`
        *,
        orders (
          id,
          customer_id,
          customers (
            name,
            whatsapp_number
          ),
          order_items (
            id,
            product_name,
            quantity,
            price
          )
        )
      `)
      .order('requested_at', { ascending: false });

    if (!error && data) {
      setReturns(data.map(formatReturn));
    }

    setLoading(false);
  };

  const setupRealtime = () => {
    supabase
      .channel('returns')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'return_products' },
        () => fetchReturns()
      )
      .subscribe();
  };

  const formatReturn = (returnData: any): ReturnRequest => {
    const order = returnData.orders || {};
    const customer = order.customers || {};

    const items = Array.isArray(order.order_items)
      ? order.order_items.map((item: any) => ({
          id: item.id,
          product_name: item.product_name,
          quantity: item.quantity,
          price: item.price
        }))
      : [];

    return {
      id: returnData.id,
      orderId: returnData.order_id,
      customerName: customer.name || 'N/A',
      customerPhone: customer.whatsapp_number || 'N/A',
      items,
      reason: returnData.return_reason,
      status: returnData.status as ReturnStatus,
      requestDate: returnData.requested_at,
      processedAt: returnData.processed_at,
      processedBy: returnData.processed_by,
      rejectionReason: returnData.rejection_reason,
      returnedDate: returnData.returned_date,
    };
  };
  const [selectedReturn, setSelectedReturn] = useState<ReturnRequest | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<ReturnStatus | 'all'>('all');
  const [isCreating, setIsCreating] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');
  const [adminComments, setAdminComments] = useState('');
  const [newReturn, setNewReturn] = useState<{
    orderId: string;
    items: string[];
    reason: string;
  }>({
    orderId: '',
    items: [],
    reason: '',
  });
  const [selectedOrderItems, setSelectedOrderItems] = useState<OrderItemWithSelection[]>([]);

  type OrderItemWithSelection = OrderItem & {
    selected: boolean;
    id: string;
    product_name: string;
    quantity: number;
    price: number;
    order_id: string;
  };

  const filteredReturns = returns.filter((returnRequest) => {
    const matchesSearch =
      returnRequest.orderId?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      returnRequest.customerName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (Array.isArray(returnRequest.items) &&
        returnRequest.items.some((item: OrderItem) =>
          item.product_name?.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );

    const matchesStatus = statusFilter === 'all' || returnRequest.status === statusFilter;

    return matchesSearch && matchesStatus;
  });


  const handleStatusChange = async (returnId: string, newStatus: ReturnStatus) => {
    if (!['Requested', 'Approved', 'Rejected', 'Returned'].includes(newStatus)) {
      console.error('Invalid status:', newStatus);
      return;
    }
    const updates: any = {
      status: newStatus,
      processed_at: new Date().toISOString(),
    };

    if (newStatus === 'Rejected') {
      updates.rejection_reason = rejectionReason;
    }

    if (newStatus === 'Returned' as ReturnStatus) {
      updates.returned_date = new Date().toISOString();
    }

    const { error } = await supabase
      .from('return_products')
      .update(updates)
      .eq('id', returnId);

    if (!error) {
      setSelectedReturn(null);
      setRejectionReason('');
      setAdminComments('');
      fetchReturns();
    } else {
      console.error('Error updating return status:', error);
    }
  };


  const handleCreateReturn = async () => {
    const { data: orderData } = await supabase
      .from('orders')
      .select('customer_id')
      .eq('id', newReturn.orderId)
      .single();

    if (!orderData) return;

    const selectedItems = selectedOrderItems.filter((item) => item.selected);

    const inserts = selectedItems.map((item) => ({
      order_id: newReturn.orderId,
      product_name: item.product_name,
      quantity: item.quantity,
      return_reason: newReturn.reason,
      status: 'Requested',
      requested_at: new Date().toISOString()
    }));

    const { error } = await supabase
      .from('return_products')
      .insert(inserts);

    if (!error) {
      setIsCreating(false);
      setNewReturn({ orderId: '', items: [], reason: '' });
      setSelectedOrderItems([]);
      fetchReturns();
    } else {
      console.error('Return creation failed:', error);
    }
  };


  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Returns Management</h2>
        <p className="text-muted-foreground">
          View and manage customer return requests
        </p>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Return Requests</CardTitle>
            <Button onClick={() => setIsCreating(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Return
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by order ID, customer, or item..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9"
              />
            </div>
            <Select
              value={statusFilter}
              onValueChange={(value) => setStatusFilter(value as ReturnStatus | 'all')}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="Requested">Pending</SelectItem>
                <SelectItem value="Approved">Approved</SelectItem>
                <SelectItem value="Rejected">Rejected</SelectItem>
                <SelectItem value="Returned">Returned</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Order ID</TableHead>
                  <TableHead>Customer</TableHead>
                  <TableHead>Items</TableHead>
                  <TableHead>Request Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredReturns.map((returnRequest) => (
                  <TableRow key={returnRequest.id}>
                    <TableCell className="font-medium">
                      {returnRequest.orderId}
                    </TableCell>
                    <TableCell>{returnRequest.customerName}</TableCell>
                    <TableCell>
                      {returnRequest.items
                        .map((item) => `${item.product_name} (${item.quantity})`)
                        .join(', ')}
                    </TableCell>
                    <TableCell>
                      {formatDistanceToNow(new Date(returnRequest.requestDate), {
                        addSuffix: true,
                      })}
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant="secondary"
                        className={`${statusColors[returnRequest.status]} text-white`}
                      >
                        {statusNames[returnRequest.status]}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        onClick={() => setSelectedReturn(returnRequest)}
                      >
                        View Details
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Return Details Dialog */}
      <Dialog
        open={selectedReturn !== null}
        onOpenChange={() => setSelectedReturn(null)}
      >
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Return Request Details</DialogTitle>
          </DialogHeader>
          {selectedReturn && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm text-muted-foreground">Order ID</Label>
                  <p className="font-medium">{selectedReturn.orderId}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">Status</Label>
                  <Badge
                    variant="secondary"
                    className={`${
                      statusColors[selectedReturn.status]
                    } text-white mt-1`}
                  >
                    {statusNames[selectedReturn.status]}
                  </Badge>
                </div>
              </div>

              <div>
                <Label className="text-sm text-muted-foreground">
                  Customer Information
                </Label>
                <div className="space-y-1">
                  <p className="font-medium">{selectedReturn.customerName}</p>
                  <p className="text-sm text-muted-foreground">
                    {selectedReturn.customerPhone}
                  </p>
                </div>
              </div>

              <div>
                <Label className="text-sm text-muted-foreground">
                  Return Items
                </Label>
                <div className="space-y-2 mt-2">
                    {selectedReturn.items.map((item) => (
                    <div
                      key={item.id}
                      className="flex justify-between items-center"
                    >
                      <span>
                        {item.product_name} x {item.quantity}
                      </span>
                      <span>₹{item.price * item.quantity}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <Label className="text-sm text-muted-foreground">
                  Return Reason
                </Label>
                <p className="mt-1">{selectedReturn.reason}</p>
              </div>

              {selectedReturn.status === 'Requested' && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Admin Comments</Label>
                    <Textarea
                      value={adminComments}
                      onChange={(e) => setAdminComments(e.target.value)}
                      placeholder="Add any comments about this return request..."
                    />
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      className="border-red-500 text-red-500 hover:bg-red-50"
                      onClick={() => {
                        if (rejectionReason.trim()) {
                          handleStatusChange(selectedReturn.id, 'Rejected');
                        } else {
                          setRejectionReason('Please provide a reason');
                        }
                      }}
                    >
                      <X className="h-4 w-4 mr-2" />
                      Reject
                    </Button>
                    <Button
                      variant="outline"
                      className="border-blue-500 text-blue-500 hover:bg-blue-50"
                      onClick={() => handleStatusChange(selectedReturn.id, 'Approved')}
                    >
                      <Check className="h-4 w-4 mr-2" />
                      Approve
                    </Button>
                  </div>
                </div>
              )}

              {selectedReturn.status === 'Approved' && (
                <div className="flex justify-end">
                  <Button
                    onClick={() => handleStatusChange(selectedReturn.id, 'Returned' as ReturnStatus)}
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Mark as Returned
                  </Button>
                </div>
              )}

              {selectedReturn.status === 'Rejected' && (
                <div>
                  <Label className="text-sm text-muted-foreground">
                    Rejection Reason
                  </Label>
                  <p className="mt-1 text-red-500">
                    {selectedReturn.rejectionReason}
                  </p>
                </div>
              )}

              {selectedReturn.status === 'Returned' && (
                <div>
                  <Label className="text-sm text-muted-foreground">
                    Return Completed
                  </Label>
                  <p className="mt-1">
                    {selectedReturn.returnedDate &&
                      format(new Date(selectedReturn.returnedDate), 'PPP')}
                  </p>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Create Return Dialog */}
      <Dialog open={isCreating} onOpenChange={setIsCreating}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Create Return Request</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label>Order ID</Label>
              <Select
  value={newReturn.orderId}
  onValueChange={async (value) => {
    setNewReturn({ ...newReturn, orderId: value });

    // Fetch items from Supabase
      const { data, error } = await supabase
      .from('order_items')
      .select('id, product_name, quantity, price, order_id')
      .eq('order_id', value)
      .returns<OrderItem[]>();

    if (!error && data) {
      setSelectedOrderItems(data.map((item) => ({ ...item, selected: false })));

    } else {
      setSelectedOrderItems([]);
    }
  }}
>
                <SelectTrigger>
                  <SelectValue placeholder="Select order" />
                </SelectTrigger>
                <SelectContent>
  {orderOptions.map((order) => (
    <SelectItem key={order.id} value={order.id}>
      {order.id}
    </SelectItem>
  ))}
</SelectContent>
              </Select>
            </div>

            {newReturn.orderId && (
  <>
<div className="space-y-2">
  <Label>Select Items</Label>
  <div className="space-y-2">
    {selectedOrderItems.length > 0 ? (
      selectedOrderItems.map((item, index) => (
        <div key={item.id} className="flex justify-between items-center">
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={item.selected}
              onChange={(e) => {
                const updated = [...selectedOrderItems];
                updated[index].selected = e.target.checked;
                setSelectedOrderItems(updated);

                // Update selected items into newReturn.items
                const selectedNames = updated
                  .filter(i => i.selected)
                  .map(i => i.product_name);

                setNewReturn((prev) => ({
                  ...prev,
                  items: selectedNames,
                }));
              }}
            />
            {item.product_name} x {item.quantity}
          </label>
          <span>₹{item.price * item.quantity}</span>
        </div>
      ))
    ) : (
      <div className="text-sm text-muted-foreground">
        No items found for this order.
      </div>
    )}
  </div>
</div>


    <div className="space-y-2">
      <Label>Return Reason</Label>
      <Textarea
        value={newReturn.reason}
        onChange={(e) =>
          setNewReturn({ ...newReturn, reason: e.target.value })
        }
        placeholder="Enter the reason for return..."
      />
    </div>
  </>
)}

          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreating(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleCreateReturn}
              disabled={!newReturn.orderId || !newReturn.items.length || !newReturn.reason}
            >
              Create Return Request
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
