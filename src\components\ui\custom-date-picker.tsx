import { useState } from 'react';
import { format } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { CustomCalendar, CustomDateRangeCalendar } from './custom-calendar';

interface DatePickerProps {
  date: Date | null;
  setDate: (date: Date | null) => void;
  label?: string;
  className?: string;
}

export function CustomDatePicker({
  date,
  setDate,
  label = "Pick a date",
  className,
}: DatePickerProps) {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal",
            !date && "text-muted-foreground",
            className
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date ? format(date, 'PPP') : <span>{label}</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <div className="p-3 border-b">
          <h3 className="text-sm font-medium">{label}</h3>
        </div>
        <CustomCalendar
          value={date}
          onChange={(newDate) => {
            setDate(newDate);
          }}
          className="border-0"
        />
      </PopoverContent>
    </Popover>
  );
}

interface DateRangePickerProps {
  dateRange: { from: Date | null; to: Date | null };
  setDateRange: (range: { from: Date | null; to: Date | null }) => void;
  className?: string;
}

export function CustomDateRangePicker({
  dateRange,
  setDateRange,
  className,
}: DateRangePickerProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant="outline"
            className={cn(
              "w-full justify-start text-left font-normal",
              !dateRange.from && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {dateRange.from ? (
              dateRange.to ? (
                <>
                  {format(dateRange.from, 'PPP')} - {format(dateRange.to, 'PPP')}
                </>
              ) : (
                format(dateRange.from, 'PPP')
              )
            ) : (
              <span>Pick a date range</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="p-3 border-b">
            <h3 className="text-sm font-medium">Select date range</h3>
          </div>
          <CustomDateRangeCalendar
            value={dateRange}
            onChange={(range) => {
              setDateRange(range);
              // Close the popover when a complete range is selected
              if (range.from && range.to) {
                setTimeout(() => setIsOpen(false), 300);
              }
            }}
            className="border-0"
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}
