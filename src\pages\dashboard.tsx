import { Card, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { ArrowUpRight, Package, Users, CreditCard, TrendingUp } from 'lucide-react';
import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase.config';

const data = [
  { name: 'Mon', value: 400 },
  { name: 'Tue', value: 300 },
  { name: 'Wed', value: 500 },
  { name: 'Thu', value: 450 },
  { name: 'Fri', value: 600 },
  { name: 'Sat', value: 550 },
  { name: 'Sun', value: 700 },
];

export function Dashboard() {
  const [totalOrders, setTotalOrders] = useState<number>(0);
  const [pendingOrders, setPendingOrders] = useState<number>(0);
  const [newCustomers, setNewCustomers] = useState<number>(0);
  const [revenue, setRevenue] = useState<number>(0);
  const [totalOrdersError, setTotalOrdersError] = useState<any>(null);
  const [pendingOrdersError, setPendingOrdersError] = useState<any>(null);
  const [newCustomersError, setNewCustomersError] = useState<any>(null);
  const [revenueError, setRevenueError] = useState<any>(null);

  useEffect(() => {
    async function fetchMetrics() {
      // Fetch total orders
      const { data: totalOrdersData, error: totalOrdersError } = await supabase
        .from('orders')
        .select('*', { count: 'exact' });
      if (totalOrdersError) {
        console.error('Error fetching total orders:', totalOrdersError);
        setTotalOrdersError(totalOrdersError);
      } else setTotalOrders(totalOrdersData?.length || 0);

      // Fetch pending orders
      const { data: pendingOrdersData, error: pendingOrdersError } = await supabase
        .from('orders')
        .select('*', { count: 'exact' })
        .eq('status', 'New'); // Using 'New' status for pending orders
      if (pendingOrdersError) {
        console.error('Error fetching pending orders:', pendingOrdersError);
        setPendingOrdersError(pendingOrdersError);
      } else setPendingOrders(pendingOrdersData?.length || 0);

      // Fetch new customers (e.g., last 30 days - adjust as needed)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const { data: newCustomersData, error: newCustomersError } = await supabase
        .from('customers')
        .select('*', { count: 'exact' })
        .gte('created_at', thirtyDaysAgo.toISOString());
      if (newCustomersError) {
        console.error('Error fetching new customers:', newCustomersError);
        setNewCustomersError(newCustomersError);
      } else setNewCustomers(newCustomersData?.length || 0);

      // Fetch total revenue (sum of total_amount from payments table - adjust as needed)
      const { data: revenueData, error: revenueError } = await supabase
        .from('payments')
        .select('amount');
      if (revenueError) {
        console.error('Error fetching revenue:', revenueError);
        setRevenueError(revenueError);
      } else {
        const totalRevenue = revenueData?.reduce((sum, payment) => sum + payment.amount, 0) || 0;
        setRevenue(totalRevenue);
      }
    }

    fetchMetrics();

    const ordersChannel = supabase
      .channel('orders_channel')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'orders' },
        (_payload) => {
          fetchMetrics();
        }
      )
      .subscribe();

    const customersChannel = supabase
      .channel('customers_channel')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'customers' },
        (_payload) => {
          fetchMetrics();
        }
      )
      .subscribe();

    const paymentsChannel = supabase
      .channel('payments_channel')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'payments' },
        (_payload) => {
          fetchMetrics();
        }
      )
      .subscribe();


      return () => {
        supabase.removeChannel(ordersChannel);
        supabase.removeChannel(customersChannel);
        supabase.removeChannel(paymentsChannel);
      };
    }, []);

  return (
    <div className="space-y-8">
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="bg-gradient-to-br from-indigo-500 to-purple-500 text-white">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium text-white/90">Total Orders</CardTitle>
            <Package className="h-4 w-4 text-white/90" />
          </CardHeader>
          <CardContent>
            {totalOrdersError ? (
              <div className="text-red-500">{totalOrdersError.message}</div>
            ) : (
              <>
                <div className="text-2xl font-bold">{totalOrders || 'TBD'}</div>
                <div className="mt-1 flex items-center text-sm text-white/80">
                  <ArrowUpRight className="mr-1 h-4 w-4" />
                  12% from last month
                </div>
              </>
            )}
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-pink-500 to-rose-500 text-white">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium text-white/90">Pending Orders</CardTitle>
            <TrendingUp className="h-4 w-4 text-white/90" />
          </CardHeader>
          <CardContent>
            {pendingOrdersError ? (
              <div className="text-red-500">{pendingOrdersError.message}</div>
            ) : (
              <>
                <div className="text-2xl font-bold">{pendingOrders || 'TBD'}</div>
                <div className="mt-1 flex items-center text-sm text-white/80">
                  Need immediate attention
                </div>
              </>
            )}
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-emerald-500 to-teal-500 text-white">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium text-white/90">New Customers</CardTitle>
            <Users className="h-4 w-4 text-white/90" />
          </CardHeader>
          <CardContent>
            {newCustomersError ? (
              <div className="text-red-500">{newCustomersError.message}</div>
            ) : (
              <>
                <div className="text-2xl font-bold">{newCustomers || 'TBD'}</div>
                <div className="mt-1 flex items-center text-sm text-white/80">
                  <ArrowUpRight className="mr-1 h-4 w-4" />
                  8% increase
                </div>
              </>
            )}
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-amber-500 to-orange-500 text-white">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium text-white/90">Revenue</CardTitle>
            <CreditCard className="h-4 w-4 text-white/90" />
          </CardHeader>
          <CardContent>
            {revenueError ? (
              <div className="text-red-500">{revenueError.message}</div>
            ) : (
              <>
                <div className="text-2xl font-bold">₹{revenue || 'TBD'}</div>
                <div className="mt-1 flex items-center text-sm text-white/80">
                  <ArrowUpRight className="mr-1 h-4 w-4" />
                  15% growth
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      <Card className="p-6">
        <CardHeader>
          <CardTitle>Weekly Orders Trend</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={data}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey="value"
                  stroke="#8884d8"
                  strokeWidth={2}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
