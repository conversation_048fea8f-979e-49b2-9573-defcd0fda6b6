import { useState, useEffect } from 'react';
import type { Order, OrderItem, Customer, Address, OrderStatus } from '@/types';
import {
  DragDropContext,
  Droppable,
  Draggable,
  DropResult,
} from '@hello-pangea/dnd';
import { Card, CardContent } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { formatDistanceToNow, format, subDays, subMonths, startOfDay, endOfDay } from 'date-fns';
import { ArrowRight, Printer, X, Edit2, MapPin } from 'lucide-react';
import { supabase, supabaseKey } from '@/lib/supabase.config';
import { CustomDateRangePicker } from '@/components/ui/custom-date-picker';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

const orderStatuses: OrderStatus[] = ['New', 'Processing', 'Out for Delivery', 'Delivered', 'Cancelled'];
const statusColors = {
  New: 'bg-blue-500',
  Processing: 'bg-yellow-500',
  'Out for Delivery': 'bg-purple-500',
  Delivered: 'bg-green-500',
  Cancelled: 'bg-red-500',
};
const statusNames = {
  New: 'New Orders',
  Processing: 'Processing',
  'Out for Delivery': 'Out for Delivery',
  Delivered: 'Delivered',
  Cancelled: 'Cancelled',
};

export function Orders() {
  type ExtendedOrder = Order & {
    customers?: Customer & {
      addresses?: Address[];
    };
    order_items?: OrderItem[];
    order_address?: Address; // For the specific address linked to this order
  };

  const [orders, setOrders] = useState<ExtendedOrder[]>([]);
  const [allOrders, setAllOrders] = useState<ExtendedOrder[]>([]);
  const [selectedOrder, setSelectedOrder] = useState<ExtendedOrder | null>(null);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [cancelReason, setCancelReason] = useState('');
  const [productDetails, setProductDetails] = useState<Record<string, {
    title: string;
    description: string;
    price: number;
    stock: number;
    isAvailable: boolean;
    imageUrl: string | null;
    productId: number | null;
  }>>({});

  // State for status change confirmation
  const [pendingStatusChange, setPendingStatusChange] = useState<{
    orderId: string;
    sourceStatus: OrderStatus;
    destinationStatus: OrderStatus;
    order: ExtendedOrder | null;
  } | null>(null);

  // Address editing state
  const [showAddressEditDialog, setShowAddressEditDialog] = useState(false);
  const [customerAddresses, setCustomerAddresses] = useState<Address[]>([]);
  const [selectedAddressId, setSelectedAddressId] = useState<string>('');
  const [showStatusChangeDialog, setShowStatusChangeDialog] = useState(false);

  // Date filtering state
  const [dateRange, setDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({
    from: undefined,
    to: undefined,
  });
  const [dateFilterOption, setDateFilterOption] = useState<string>("all");

  const fetchProductDetails = async (productName: string) => {
    try {
      // Query the local products table using the product name
      const { data, error } = await supabase
        .from('products')
        .select('id, product_id, name, description, price, stock_quantity, is_available, image_url')
        .eq('name', productName)
        .single();

      if (error) {
        console.error('Error fetching product from database:', error);
        throw error;
      }

      if (!data) {
        throw new Error('Product not found');
      }

      return {
        title: data.name || 'Unknown Product',
        description: data.description || 'No description available',
        price: data.price || 0,
        stock: data.stock_quantity || 0,
        isAvailable: data.is_available || false,
        imageUrl: data.image_url || null,
        productId: data.product_id || null
      };
    } catch (error) {
      console.error('Detailed error in fetchProductDetails:', {
        productName,
        error: error instanceof Error ? error.message : 'Unknown error',
        fullError: error
      });

      return {
        title: `Product ${productName}`,
        description: 'Failed to load product details',
        price: 0,
        stock: 0,
        isAvailable: false,
        imageUrl: null,
        productId: null
      };
    }
  };

  // Apply date filters to orders
  const applyDateFilter = (orders: ExtendedOrder[]) => {
    if (dateFilterOption === 'all') {
      return orders;
    }

    let fromDate: Date | undefined;
    let toDate: Date | undefined = new Date();

    switch (dateFilterOption) {
      case 'last-week':
        fromDate = subDays(new Date(), 7);
        break;
      case 'last-2-weeks':
        fromDate = subDays(new Date(), 14);
        break;
      case 'last-month':
        fromDate = subMonths(new Date(), 1);
        break;
      case 'custom':
        fromDate = dateRange.from ? startOfDay(dateRange.from) : undefined;
        toDate = dateRange.to ? endOfDay(dateRange.to) : undefined;
        break;
    }

    if (!fromDate) {
      return orders;
    }

    return orders.filter(order => {
      const orderDate = new Date(order.created_at);
      if (fromDate && toDate) {
        return orderDate >= fromDate && orderDate <= toDate;
      }
      return orderDate >= fromDate!;
    });
  // eslint-disable-next-line react-hooks/exhaustive-deps
  };

  // Fetch orders from Supabase
  const fetchOrders = async () => {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        customers (
          name,
          whatsapp_number,
          addresses (
            id,
            address,
            pincode,
            is_default
          )
        ),
        order_items ( id, product_name, quantity, price ),
        order_address:addresses!address_id (
          id,
          address,
          pincode,
          is_default
        )
      `)
      .or('payment_method.eq.COD,and(payment_method.eq.ONLINE,payment_status.eq.PAID)')
      .returns<ExtendedOrder[]>();

    if (!error) {
      setAllOrders(data || []);
      setOrders(applyDateFilter(data || []));
    } else {
      console.error('Error fetching orders:', error);
    }
  };

  // Apply date filters when filter option changes
  useEffect(() => {
    setOrders(applyDateFilter(allOrders));
  }, [dateFilterOption, dateRange, allOrders]);

  // Initial fetch and realtime subscription
  useEffect(() => {
    fetchOrders();

    const channel = supabase
      .channel('orders-realtime')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'orders' }, () => {
        fetchOrders();
        return Promise.resolve();
      })
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  const handleDragEnd = async (result: DropResult) => {
    if (!result.destination) return;

    const { source, destination, draggableId } = result;
    if (source.droppableId === destination.droppableId) return;

    const sourceStatus = source.droppableId as OrderStatus;
    const destinationStatus = destination.droppableId as OrderStatus;

    // Prevent moving orders that are already in Delivered status
    if (sourceStatus === 'Delivered') {
      console.log('Cannot move orders from Delivered status');
      return;
    }

    // Fetch the latest order data to ensure we have the most up-to-date payment information
    const { data: latestOrderData, error: fetchError } = await supabase
      .from('orders')
      .select(`
        *,
        customers (
          name,
          whatsapp_number,
          addresses (
            id,
            address,
            pincode,
            is_default
          )
        ),
        order_items ( id, product_name, quantity, price ),
        order_address:addresses!address_id (
          id,
          address,
          pincode,
          is_default
        )
      `)
      .eq('id', draggableId)
      .single();

    if (fetchError) {
      console.error('Error fetching latest order data:', fetchError);
      return;
    }

    const order = latestOrderData as ExtendedOrder;

    // Handle cancellation separately
    if (destinationStatus === 'Cancelled') {
      setSelectedOrder(order);
      setShowCancelDialog(true);
      return;
    }

    // Get the valid next status
    const validNextStatus = getNextStatus(sourceStatus);

    // If trying to move to any status other than the valid next status, return
    if (destinationStatus !== validNextStatus) {
      // Reset the UI to original position
      return;
    }

    console.log('Drag end - order details:', {
      orderId: draggableId,
      sourceStatus,
      destinationStatus,
      paymentMethod: order.payment_method,
      paymentStatus: order.payment_status
    });

    // Store the pending status change and show confirmation dialog
    setPendingStatusChange({
      orderId: draggableId,
      sourceStatus,
      destinationStatus,
      order
    });
    setShowStatusChangeDialog(true);
  };

  // Function to handle confirmed status change
  const handleConfirmStatusChange = async () => {
    if (!pendingStatusChange) return;

    const { orderId, destinationStatus, order } = pendingStatusChange;

    // Prepare update data
    const updateData: {
      status: OrderStatus;
      payment_status?: string;
      payment_capture_date?: string;
      updated_at: string;
    } = {
      status: destinationStatus,
      updated_at: new Date().toISOString()
    };

    // Debug information
    console.log('Order before update:', {
      id: orderId,
      destinationStatus,
      currentPaymentMethod: order?.payment_method,
      currentPaymentStatus: order?.payment_status
    });

    // If moving to Delivered status and payment method is COD with Pending payment status,
    // update payment_status to PAID
    if (destinationStatus === 'Delivered') {
      console.log('Destination is Delivered, checking payment conditions');

      if (order?.payment_method === 'COD') {
        console.log('Payment method is COD');

        // Check if payment status is not already PAID
        if (!order.payment_status || order.payment_status === 'Pending' || (order.payment_status && order.payment_status.toUpperCase() !== 'PAID')) {
          console.log('Payment status is not PAID, updating to PAID');
          updateData.payment_status = 'PAID';
          updateData.payment_capture_date = new Date().toISOString();
        } else {
          console.log('Payment status is already:', order.payment_status);
        }
      } else {
        console.log('Payment method is not COD:', order?.payment_method);
      }
    }

    console.log('Final update data:', updateData);

    const { data, error } = await supabase
      .from('orders')
      .update(updateData)
      .eq('id', orderId)
      .select();

    if (error) {
      console.error('Error updating order status:', error);
    } else {
      console.log('Update successful, response:', data);
      setOrders(orders.map(order =>
        order.id === orderId ? { ...order, ...updateData } : order
      ));

      // Refresh orders to ensure we have the latest data
      fetchOrders();
    }

    // Reset the pending status change and close the dialog
    setPendingStatusChange(null);
    setShowStatusChangeDialog(false);
  };

  // Function to cancel the status change
  const handleCancelStatusChange = () => {
    setPendingStatusChange(null);
    setShowStatusChangeDialog(false);
  };

  const getNextStatus = (currentStatus: OrderStatus): OrderStatus | null => {
    const statusFlow = {
      'New': 'Processing',
      'Processing': 'Out for Delivery',
      'Out for Delivery': 'Delivered',
      'Delivered': null,
      'Cancelled': null
    };

    return statusFlow[currentStatus] as OrderStatus | null;
  };

  const handleMoveToNextStage = async (orderId: string) => {
    // Fetch the latest order data to ensure we have the most up-to-date payment information
    const { data: latestOrderData, error: fetchError } = await supabase
      .from('orders')
      .select(`
        *,
        customers (
          name,
          whatsapp_number,
          addresses (
            id,
            address,
            pincode,
            is_default
          )
        ),
        order_items ( id, product_name, quantity, price ),
        order_address:addresses!address_id (
          id,
          address,
          pincode,
          is_default
        )
      `)
      .eq('id', orderId)
      .single();

    if (fetchError) {
      console.error('Error fetching latest order data:', fetchError);
      return;
    }

    const order = latestOrderData as ExtendedOrder;
    if (!order || !order.status) return;

    // Prevent moving orders that are already in Delivered status
    if (order.status === 'Delivered') {
      console.log('Cannot move orders from Delivered status');
      return;
    }

    const nextStatus = getNextStatus(order.status);
    if (!nextStatus) return;

    console.log('Moving order to next stage:', {
      orderId,
      currentStatus: order.status,
      nextStatus,
      paymentMethod: order.payment_method,
      paymentStatus: order.payment_status
    });

    // Store the pending status change and show confirmation dialog
    setPendingStatusChange({
      orderId,
      sourceStatus: order.status,
      destinationStatus: nextStatus,
      order
    });

    // Close the order details dialog and open the confirmation dialog
    setSelectedOrder(null);
    setShowStatusChangeDialog(true);
  };

  const handleCancelOrder = async () => {
    if (!selectedOrder || !cancelReason.trim()) return;

    // Clean update with only required fields
    const updateData = {
      status: 'Cancelled' as const,
      cancellation_reason: cancelReason,
      updated_at: new Date().toISOString()
    };

    const { error } = await supabase
      .from('orders')
      .update(updateData)
      .eq('id', selectedOrder.id);

    if (!error) {
      setOrders(orders.map(order =>
        order.id === selectedOrder.id ? { ...order, ...updateData } : order
      ));
      setShowCancelDialog(false);
      setSelectedOrder(null);
      setCancelReason('');
    } else {
      console.error('Failed to cancel order:', error);
    }
  };

  // Address management functions
  const fetchCustomerAddresses = async (customerId: string) => {
    const { data, error } = await supabase
      .from('addresses')
      .select('*')
      .eq('customer_id', customerId)
      .returns<Address[]>();

    if (!error) {
      setCustomerAddresses(data || []);
    } else {
      console.error('Error fetching customer addresses:', error);
    }
  };

  const handleEditAddress = async () => {
    if (!selectedOrder?.customer_id) return;

    await fetchCustomerAddresses(selectedOrder.customer_id);

    // Set the current address as selected, or default if no address_id
    const currentAddressId = selectedOrder.address_id ||
      selectedOrder.customers?.addresses?.find(a => a.is_default)?.id || '';
    setSelectedAddressId(currentAddressId);
    setShowAddressEditDialog(true);
  };

  const handleUpdateOrderAddress = async () => {
    if (!selectedOrder || !selectedAddressId) return;

    const { error } = await supabase
      .from('orders')
      .update({
        address_id: selectedAddressId,
        updated_at: new Date().toISOString()
      })
      .eq('id', selectedOrder.id);

    if (!error) {
      // Update the local state
      const updatedOrder = { ...selectedOrder, address_id: selectedAddressId };
      setSelectedOrder(updatedOrder);
      setOrders(orders.map(order =>
        order.id === selectedOrder.id ? updatedOrder : order
      ));
      setShowAddressEditDialog(false);

      // Refresh the order data to get the updated address
      await fetchOrders();
    } else {
      console.error('Failed to update order address:', error);
    }
  };

  const handleCancelAddressEdit = () => {
    setShowAddressEditDialog(false);
    setSelectedAddressId('');
    setCustomerAddresses([]);
  };


  const handlePrintInvoice = (order: ExtendedOrder) => {
    const invoiceContent = `
      <html><head><title>Invoice - ${order.id}</title>
      <style>body{font-family:sans-serif;margin:20px}table{width:100%;border-collapse:collapse}th,td{border:1px solid #ddd;padding:8px;text-align:left}.total{text-align:right;margin-top:20px}</style>
      </head><body>
      <h2>Dhanam Store</h2>
      <p>Invoice ID: ${order.id}</p>
      <p>Date: ${format(new Date(order.created_at), 'PPP')}</p>
      <h3>Customer Details</h3>
      <p>Name: ${order.customers?.name || 'N/A'}</p>
      <p>Phone: ${order.customers?.whatsapp_number || 'N/A'}</p>
      ${order.status === 'Cancelled' && order.cancellation_reason
        ? `<p style="color: red;"><strong>Cancelled Reason:</strong> ${order.cancellation_reason}</p>`
        : ''}
      <table><thead><tr><th>Item</th><th>ID</th><th>Qty</th><th>Price</th><th>Total</th></tr></thead><tbody>
      ${order.order_items?.map(item => `
        <tr>
          <td>${productDetails[item.product_name]?.title || item.product_name}</td>
          <td>${item.product_name}</td>
          <td>${item.quantity}</td>
          <td>₹${item.price}</td>
          <td>₹${item.price * item.quantity}</td>
        </tr>`).join('')}
      </tbody></table>
      <div class="total"><h3>Total: ₹${order.total_amount}</h3></div>
      </body></html>
    `;
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(invoiceContent);
      printWindow.document.close();
      printWindow.print();
    }
  };

  useEffect(() => {
    async function fetchOrderProductDetails() {
      if (!selectedOrder?.order_items) return;

      const details: Record<string, {
        title: string;
        description: string;
        price: number;
        stock: number;
        isAvailable: boolean;
        imageUrl: string | null;
        productId: number | null;
      }> = {};

      // Fetch details for all products in parallel
      await Promise.all(
        selectedOrder.order_items.map(async (item) => {
          try {
            const productDetail = await fetchProductDetails(item.product_name);
            if (productDetail) {
              details[item.product_name] = productDetail;
            }
          } catch (error) {
            console.error(`Error fetching details for product ${item.product_name}:`, error);
          }
        })
      );

      setProductDetails(details);
    }

    if (selectedOrder) {
      fetchOrderProductDetails();
    }
  }, [selectedOrder]);

  // Clear date filters
  const clearDateFilters = () => {
    setDateFilterOption('all');
    setDateRange({ from: undefined, to: undefined });
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Orders</h2>
        <p className="text-muted-foreground">Manage and track customer orders</p>
      </div>

      {/* Date Filter Controls */}
      <div className="flex flex-wrap items-center gap-4 bg-gray-50 p-4 rounded-lg border">
        <div>
          <Select
            value={dateFilterOption}
            onValueChange={setDateFilterOption}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by date" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Orders</SelectItem>
              <SelectItem value="last-week">Last Week</SelectItem>
              <SelectItem value="last-2-weeks">Last 2 Weeks</SelectItem>
              <SelectItem value="last-month">Last Month</SelectItem>
              <SelectItem value="custom">Custom Range</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {dateFilterOption === 'custom' && (
          <div className="flex-grow">
            <CustomDateRangePicker
              dateRange={{ from: dateRange.from || null, to: dateRange.to || null }}
              setDateRange={(range) => setDateRange({ from: range.from || undefined, to: range.to || undefined })}
              className="w-full max-w-sm"
            />
          </div>
        )}

        {dateFilterOption !== 'all' && (
          <Button variant="outline" size="icon" onClick={clearDateFilters} title="Clear filters">
            <X className="h-4 w-4" />
          </Button>
        )}

        <div className="ml-auto">
          <Badge variant="outline" className="text-sm">
            {orders.length} orders
          </Badge>
        </div>
      </div>

      <DragDropContext onDragEnd={handleDragEnd}>
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
          {orderStatuses.map(status => (
            <div key={status} className="space-y-4">
              <div className="flex items-center space-x-2">
    <div className={`w-2 h-2 rounded-full ${statusColors[status as keyof typeof statusColors]}`} />
    <h3 className="font-semibold">{statusNames[status as keyof typeof statusNames]}</h3>
                <Badge variant="secondary">{orders.filter(o => o.status === status).length}</Badge>
              </div>
              {status === 'Delivered' && (
                <div className="text-xs text-muted-foreground italic">
                  Orders in this column cannot be moved
                </div>
              )}
              <Droppable droppableId={status}>
                {provided => (
                  <div ref={provided.innerRef} {...provided.droppableProps} className="space-y-2 min-h-[500px] bg-gray-50 p-4 rounded-lg border border-dashed">
                    {orders.filter(o => o.status === status).map((order, index) => (
                      <Draggable key={order.id} draggableId={order.id} index={index}>
                        {provided => (
                          <>
                            <Card
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              className={`${status === 'Delivered' ? 'cursor-not-allowed' : 'cursor-grab'} hover:shadow-md`}
                              onClick={() => setSelectedOrder(order)}>
                              <CardContent className="p-4">
                                <div className="font-medium text-lg mb-1">{order.customers?.name || 'Unknown'}</div>
                                <div className="text-sm text-muted-foreground">{order.order_items?.length || 0} items</div>
                                <div className="text-xs text-muted-foreground">{formatDistanceToNow(new Date(order.created_at), { addSuffix: true })}</div>
                                <div className="mt-2 pt-2 border-t flex justify-end">
                                  <Badge variant="secondary" className="text-lg">₹{order.total_amount}</Badge>
                                </div>
                              </CardContent>
                            </Card>
                            {index < orders.filter(o => o.status === status).length - 1 && <Separator className="my-2" />}
                          </>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </div>
          ))}
        </div>
      </DragDropContext>

      <Dialog open={!!selectedOrder} onOpenChange={() => setSelectedOrder(null)}>
        <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
  <div className="flex items-center justify-between">
    <div>
      <DialogTitle>Order Details</DialogTitle>
      {selectedOrder && (
        <p className="text-xs text-muted-foreground mt-1">
          Order ID: {selectedOrder.id}
        </p>
      )}
    </div>
    {selectedOrder && (
      <Button variant="outline" size="icon" onClick={() => handlePrintInvoice(selectedOrder)}>
        <Printer className="h-4 w-4" />
      </Button>
    )}
  </div>
</DialogHeader>
          {selectedOrder && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <Badge variant="outline" className="text-sm">
                  Current Stage: {statusNames[selectedOrder.status as keyof typeof statusNames]}
                </Badge>
                {getNextStatus(selectedOrder.status) && selectedOrder.status !== 'Delivered' && (
                  <Button onClick={() => handleMoveToNextStage(selectedOrder.id)} className="flex items-center gap-2">
                    Move to {statusNames[getNextStatus(selectedOrder.status)! as keyof typeof statusNames]}
                    <ArrowRight className="h-4 w-4" />
                  </Button>
                )}
              </div>
              <div>
                <h3 className="font-semibold mb-2">Customer Information</h3>
                <div className="space-y-1 text-sm">
                  <p>Name: {selectedOrder.customers?.name || 'N/A'}</p>
                  <p>Phone: {selectedOrder.customers?.whatsapp_number || 'N/A'}</p>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">Delivery Address:</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={handleEditAddress}
                          className="h-6 px-2 text-xs"
                        >
                          <Edit2 className="h-3 w-3 mr-1" />
                          Edit
                        </Button>
                      </div>
                      <p className="mt-1 ml-6">
                        {selectedOrder.order_address?.address ||
                         selectedOrder.customers?.addresses?.find?.(a => a.is_default)?.address ||
                         'No address available'}
                      </p>
                      <p className="text-xs text-muted-foreground ml-6">
                        Pincode: {selectedOrder.order_address?.pincode ||
                                 selectedOrder.customers?.addresses?.find?.(a => a.is_default)?.pincode ||
                                 'N/A'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Order Items</h3>
                <div className="space-y-2">
                  {selectedOrder?.order_items?.map(item => (
                    <div key={item.id} className="flex flex-col space-y-1">
                      <div className="flex justify-between items-center">
                        <div className="flex flex-col">
                          <span className="font-medium">
                            {productDetails[item.product_name]?.title || 'Loading...'}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            Product ID: {productDetails[item.product_name]?.productId || 'N/A'} | Name: {item.product_name}
                          </span>
                          <span className="text-sm text-muted-foreground">
                            {productDetails[item.product_name]?.description || ''}
                          </span>
                        </div>
                        <div className="text-right">
                          <span>{item.quantity} × ₹{item.price}</span>
                          <div className="text-sm font-semibold">
                            ₹{item.price * item.quantity}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                  <div className="border-t pt-2 mt-4">
                    <div className="flex justify-between">
                      <span>Delivery Charge</span>
                      <span>₹0</span>
                    </div>
                    <div className="flex justify-between font-semibold">
                      <span>Total</span>
                      <span>₹{selectedOrder?.total_amount}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Payment Details</h3>
                <div className="text-sm space-y-1">
                  <p>Method: {selectedOrder.payment_method}</p>
                  <p>Status: {selectedOrder.payment_status || 'Pending'}</p>
                  {selectedOrder.payment_capture_date && (
                    <p>Paid on: {format(new Date(selectedOrder.payment_capture_date), 'PPP')}</p>
                  )}
                </div>
              </div>
              {selectedOrder.status === 'Cancelled' && selectedOrder.cancellation_reason && (
  <div>
    <h3 className="font-semibold mb-2 text-red-600">Cancellation Reason</h3>
    <div className="text-sm text-muted-foreground">
      {selectedOrder.cancellation_reason}
    </div>
  </div>
)}
            </div>
          )}
        </DialogContent>
      </Dialog>

      <Dialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cancel Order</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label>Cancellation Reason</Label>
              <Textarea value={cancelReason} onChange={(e) => setCancelReason(e.target.value)} placeholder="Reason for cancellation..." />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => { setShowCancelDialog(false); setCancelReason(''); }}>
              Back
            </Button>
            <Button variant="destructive" onClick={handleCancelOrder} disabled={!cancelReason.trim()}>
              Cancel Order
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Status Change Confirmation Dialog */}
      <Dialog open={showStatusChangeDialog} onOpenChange={(open) => {
        if (!open) handleCancelStatusChange();
      }}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Status Change</DialogTitle>
          </DialogHeader>
          {pendingStatusChange && (
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${statusColors[pendingStatusChange.sourceStatus]}`} />
                    <span>{statusNames[pendingStatusChange.sourceStatus]}</span>
                  </div>
                  <ArrowRight className="h-5 w-5 mx-2" />
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${statusColors[pendingStatusChange.destinationStatus]}`} />
                    <span>{statusNames[pendingStatusChange.destinationStatus]}</span>
                  </div>
                </div>
              </div>

              <div className="border rounded-md p-3 bg-gray-50">
                <h4 className="font-medium mb-1">Order Details</h4>
                <p className="text-sm">Customer: {pendingStatusChange.order?.customers?.name || 'Unknown'}</p>
                <p className="text-sm">Items: {pendingStatusChange.order?.order_items?.length || 0}</p>
                <p className="text-sm">Total: ₹{pendingStatusChange.order?.total_amount}</p>
                <p className="text-sm">Payment Method: {pendingStatusChange.order?.payment_method}</p>
                <p className="text-sm">Payment Status: {pendingStatusChange.order?.payment_status || 'Pending'}</p>
              </div>

              <p className="text-sm text-muted-foreground">
                Are you sure you want to change the status of this order?
                {pendingStatusChange.destinationStatus === 'Delivered' &&
                 pendingStatusChange.order?.payment_method === 'COD' &&
                 (!pendingStatusChange.order.payment_status ||
                  pendingStatusChange.order.payment_status === 'Pending' ||
                  (pendingStatusChange.order.payment_status && pendingStatusChange.order.payment_status.toUpperCase() !== 'PAID')) && (
                  <span className="block mt-2 text-green-600 font-medium">
                    Note: Payment status will be updated to PAID upon delivery.
                  </span>
                )}
              </p>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={handleCancelStatusChange}>
              Cancel
            </Button>
            <Button onClick={handleConfirmStatusChange}>
              Confirm Change
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Address Edit Dialog */}
      <Dialog open={showAddressEditDialog} onOpenChange={setShowAddressEditDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Change Delivery Address</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-3">
              <Label className="text-sm font-medium">Select delivery address:</Label>
              <RadioGroup value={selectedAddressId} onValueChange={setSelectedAddressId}>
                {customerAddresses.map((address) => (
                  <div key={address.id} className="flex items-start space-x-3">
                    <RadioGroupItem value={address.id} id={address.id} className="mt-1" />
                    <div className="flex-1">
                      <label htmlFor={address.id} className="cursor-pointer">
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm font-medium">
                            {address.is_default && (
                              <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded mr-2">
                                Default
                              </span>
                            )}
                            Address
                          </span>
                        </div>
                        <p className="text-sm text-gray-700 mt-1 ml-6">
                          {address.address}
                        </p>
                        <p className="text-xs text-muted-foreground ml-6">
                          Pincode: {address.pincode}
                        </p>
                      </label>
                    </div>
                  </div>
                ))}
              </RadioGroup>
              {customerAddresses.length === 0 && (
                <p className="text-sm text-muted-foreground">No addresses found for this customer.</p>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={handleCancelAddressEdit}>
              Cancel
            </Button>
            <Button
              onClick={handleUpdateOrderAddress}
              disabled={!selectedAddressId || customerAddresses.length === 0}
            >
              Update Address
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
