# System Patterns

**Project Name:** Dhanam Store

**System Architecture:**

- The application follows a component-based architecture using React.
- The UI is built using Radix UI components and Tailwind CSS for styling.
- The application uses a dashboard layout with a sidebar for navigation and a header for user profile and logout.
- The application uses TypeScript for type safety and maintainability.

**Key Technical Decisions:**

- Using Vite as the build tool for fast development and optimized production builds.
- Using Radix UI for accessible and customizable UI components.
- Using Tailwind CSS for styling and responsive design.
- Using TypeScript for type safety and maintainability.

**Design Patterns in Use:**

- Component-based architecture.
- Utility-first CSS (Tailwind CSS).
- Hooks for managing state and side effects.

**Component Relationships:**

- `App` component: The main application component that handles authentication and routing.
- `LoginPage` component: The login page component.
- `DashboardRoutes` component: The component that renders the dashboard layout and routes.
- `DashboardLayout` component: The layout component for the dashboard, including the sidebar and header.
- UI components: Reusable UI components built using Radix UI, such as buttons, inputs, and dialogs.
