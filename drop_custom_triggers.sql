-- SQL script to identify and drop only custom triggers on the orders table
-- (not dropping foreign key constraint triggers)

-- First, list all triggers on the orders table
SELECT 
    tgname AS trigger_name,
    pg_get_triggerdef(t.oid) AS trigger_definition
FROM 
    pg_trigger t
WHERE 
    tgrelid = 'orders'::regclass
    AND NOT tgname LIKE 'RI_ConstraintTrigger_%'; -- Exclude foreign key constraint triggers

-- Drop custom triggers (manually review and uncomment the lines below)
/*
DO $$
DECLARE
    trigger_record RECORD;
BEGIN
    FOR trigger_record IN 
        SELECT tgname AS trigger_name
        FROM pg_trigger
        WHERE tgrelid = 'orders'::regclass
        AND NOT tgname LIKE 'RI_ConstraintTrigger_%' -- Exclude foreign key constraint triggers
    LOOP
        EXECUTE format('DROP TRIGGER IF EXISTS %I ON orders', trigger_record.trigger_name);
        RAISE NOTICE 'Dropped trigger: %', trigger_record.trigger_name;
    END LOOP;
END $$;
*/

-- After reviewing the list of triggers, uncomment the section above to drop them
