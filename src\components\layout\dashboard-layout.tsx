import React, { useState } from 'react';
import { Menu, X, Package, Users, LogOut, Store, ChevronLeft, ChevronRight, ShoppingBag, Tags } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';

interface DashboardLayoutProps {
  children: React.ReactNode;
  onLogout: () => void;
}

export function DashboardLayout({ children, onLogout }: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const navigation = [
    { name: 'Orders', href: '/orders', icon: Package },
    { name: 'Customers', href: '/customers', icon: Users },
    { name: 'Categories', href: '/categories', icon: Tags },
    { name: 'Products', href: '/products', icon: ShoppingBag },
  ];

  const handleNavigation = (e: React.MouseEvent<HTMLAnchorElement>, href: string) => {
    e.preventDefault();
    window.history.pushState({}, '', href);
    window.dispatchEvent(new PopStateEvent('popstate'));
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Sidebar */}
      <div
        className={cn(
          'fixed inset-y-0 left-0 z-50 transform bg-primary transition-all duration-200 ease-in-out md:translate-x-0',
          !sidebarOpen && '-translate-x-full',
          sidebarCollapsed ? 'w-20' : 'w-64'
        )}
      >
        <div className="flex h-20 items-center justify-between px-6">
          <div className={cn("flex items-center gap-3", sidebarCollapsed && "justify-center")}>
            <Store className="h-8 w-8 text-primary-foreground" />
            {!sidebarCollapsed && (
              <div className="flex flex-col">
                <h1 className="text-lg font-bold text-primary-foreground">Dhanam Store</h1>
              </div>
            )}
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setSidebarOpen(false)}
            className={cn("md:hidden text-primary-foreground hover:bg-primary-foreground/10", sidebarCollapsed && "hidden")}
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        <div className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2">
          <Button
            variant="secondary"
            size="icon"
            className="h-6 w-6 rounded-full shadow-md"
            onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
          >
            {sidebarCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>
        </div>

        <nav className="mt-4 space-y-1 px-3">
          {navigation.map((item, index) => (
            <React.Fragment key={item.name}>
              <a
                href={item.href}
                onClick={(e) => handleNavigation(e, item.href)}
                className={cn(
                  "flex items-center rounded-md px-4 py-3 text-sm font-medium text-primary-foreground/80 hover:bg-primary-foreground/10 hover:text-primary-foreground transition-colors",
                  sidebarCollapsed && "justify-center px-2"
                )}
              >
                <item.icon className={cn("h-5 w-5", !sidebarCollapsed && "mr-3")} />
                {!sidebarCollapsed && item.name}
              </a>
              {index < navigation.length - 1 && (
                <Separator className="my-2 bg-primary-foreground/10" />
              )}
            </React.Fragment>
          ))}
        </nav>
      </div>

      {/* Main content */}
      <div
        className={cn(
          'flex flex-col transition-all duration-200 ease-in-out',
          sidebarOpen ? (sidebarCollapsed ? 'md:pl-20' : 'md:pl-64') : ''
        )}
      >
        <div className="sticky top-0 z-40 flex h-16 shrink-0 items-center justify-between gap-x-4 border-b bg-background px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setSidebarOpen(true)}
            className={cn('md:hidden', sidebarOpen && 'hidden')}
          >
            <Menu className="h-5 w-5" />
          </Button>

          <div className="flex items-center gap-4 ml-auto">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src="/avatar.png" alt="Admin" />
                    <AvatarFallback>DS</AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end">
                <DropdownMenuItem onClick={onLogout} className="text-destructive">
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <main className="flex-1 bg-muted">
          <div className="px-4 py-8 sm:px-6 lg:px-8">{children}</div>
        </main>
      </div>
    </div>
  );
}
