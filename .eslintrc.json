{"extends": [], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "react-hooks/exhaustive-deps": "off", "prefer-const": "off", "no-console": "off", "@typescript-eslint/no-empty-function": "off", "@typescript-eslint/ban-types": "off", "@typescript-eslint/no-non-null-assertion": "off", "react/no-unescaped-entities": "off", "react/display-name": "off", "react/prop-types": "off"}, "env": {"browser": true, "es2021": true, "node": true}, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 12, "sourceType": "module"}}