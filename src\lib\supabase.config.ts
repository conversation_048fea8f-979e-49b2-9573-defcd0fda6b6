import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://phmbblwthsregggkwbfo.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBobWJibHd0aHNyZWdnZ2t3YmZvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1MDkzNDAsImV4cCI6MjA1OTA4NTM0MH0.1Obru9SaNCGLN9wdkhVsuuUjBjH5fIVJOdalByvv7Yo';

export const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: true,
    storage: localStorage
  }
});

// Export the key for use in other parts of the app
export { supabaseKey };
