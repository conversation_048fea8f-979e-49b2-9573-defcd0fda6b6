import { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Search, Plus, Pencil, Trash2, Check, X, Image } from 'lucide-react';
import { supabase } from '@/lib/supabase.config';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { ImageUpload } from '@/components/ui/image-upload';

interface Category {
  id: string;
  name: string;
}

interface Subcategory {
  id: string;
  category_id: string;
  name: string;
  category?: Category | any;
}

interface Product {
  id: string;
  product_id?: number;
  subcategory_id: string | null;
  name: string;
  description: string | null;
  image_url: string | null;
  price: number;
  stock_quantity: number;
  is_available: boolean;
  created_at: string;
  updated_at: string;
  subcategory?: Subcategory;
}

export function Products() {
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [subcategories, setSubcategories] = useState<Subcategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isAddingProduct, setIsAddingProduct] = useState(false);
  const [isEditingProduct, setIsEditingProduct] = useState(false);
  const [newProduct, setNewProduct] = useState<Partial<Product>>({
    name: '',
    description: '',
    subcategory_id: null,
    price: 0,
    stock_quantity: 0,
    is_available: true,
    image_url: '',
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<{
    category?: string;
    subcategory?: string;
    price?: string;
    stock?: string;
    image?: string;
  }>({});

  // Fetch products from Supabase
  const fetchProducts = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('products')
        .select(`
          *,
          subcategory:subcategory_id (
            id,
            name,
            category_id,
            category:category_id (
              id,
              name
            )
          )
        `)
        .order('product_id');

      if (error) {
        throw error;
      }

      setProducts(data || []);
    } catch (error: any) {
      setError(error.message);
      console.error('Error fetching products:', error);
    } finally {
      setLoading(false);
    }
  };

  // Function to update all products with 0 stock to be unavailable
  const updateZeroStockProducts = async () => {
    try {
      const { error } = await supabase
        .from('products')
        .update({
          is_available: false,
          updated_at: new Date().toISOString()
        })
        .eq('stock_quantity', 0)
        .eq('is_available', true);

      if (error) throw error;

      // Refresh the products list
      await fetchProducts();
      console.log('Updated products with zero stock to be unavailable');
    } catch (error: any) {
      console.error('Error updating zero stock products:', error);
    }
  };

  // Fetch categories for dropdown
  const fetchCategories = async () => {
    try {
      const { data: categoriesData, error: categoriesError } = await supabase
        .from('categories')
        .select('id, name')
        .order('name');

      if (categoriesError) {
        throw categoriesError;
      }

      setCategories(categoriesData || []);
    } catch (error: any) {
      console.error('Error fetching categories:', error);
    }
  };

  // Fetch subcategories for a specific category
  const fetchSubcategories = async (categoryId: string | null = null) => {
    try {
      let query = supabase
        .from('subcategories')
        .select(`
          id,
          name,
          category_id,
          category:category_id (
            id,
            name
          )
        `)
        .order('name');

      if (categoryId) {
        query = query.eq('category_id', categoryId);
      }

      const { data, error } = await query;

      if (error) {
        throw error;
      }

      setSubcategories(data || []);
    } catch (error: any) {
      console.error('Error fetching subcategories:', error);
    }
  };

  // Initial fetch and realtime subscription
  useEffect(() => {
    const initializeData = async () => {
      await fetchProducts();
      await fetchCategories();
      await fetchSubcategories();
      // Update any existing products with 0 stock to be unavailable
      await updateZeroStockProducts();
    };

    initializeData();

    const productsChannel = supabase
      .channel('products-realtime')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'products' },
        () => {
          fetchProducts();
          return Promise.resolve();
        }
      )
      .subscribe();

    const categoriesChannel = supabase
      .channel('categories-for-products')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'categories' },
        () => {
          fetchCategories();
          return Promise.resolve();
        }
      )
      .subscribe();

    const subcategoriesChannel = supabase
      .channel('subcategories-for-products')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'subcategories' },
        () => {
          fetchSubcategories(selectedCategory);
          return Promise.resolve();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(productsChannel);
      supabase.removeChannel(categoriesChannel);
      supabase.removeChannel(subcategoriesChannel);
    };
  }, []);

  // Update subcategories when category changes
  useEffect(() => {
    if (selectedCategory) {
      fetchSubcategories(selectedCategory);
    } else {
      fetchSubcategories();
    }
  }, [selectedCategory]);

  // Filter products based on search term
  const filteredProducts = products.filter((product) =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (product.description && product.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (product.subcategory?.name && product.subcategory.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (product.subcategory?.category?.name && product.subcategory.category.name.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Validate image URL
  const validateImageUrl = (url: string): boolean => {
    try {
      // Check if it's a valid URL
      const urlObj = new URL(url);

      // Must be http or https - if it is, we'll accept it as a valid image URL
      return ['http:', 'https:'].includes(urlObj.protocol);
    } catch {
      return false;
    }
  };

  // Validate product form
  const validateProductForm = (): boolean => {
    const errors: typeof validationErrors = {};

    // Validate category selection
    if (!selectedCategory) {
      errors.category = 'Please select a category.';
    }

    // Validate subcategory selection
    if (!newProduct.subcategory_id) {
      errors.subcategory = 'Please select a subcategory.';
    }

    // Validate price
    if (!newProduct.price || newProduct.price <= 0) {
      errors.price = 'Price must be greater than 0.';
    }

    // Validate stock quantity
    if (newProduct.stock_quantity === undefined || newProduct.stock_quantity === null || newProduct.stock_quantity < 0) {
      errors.stock = 'Stock quantity must be 0 or greater.';
    }

    // Validate image
    if (!newProduct.image_url) {
      errors.image = 'Please upload an image or provide an image URL.';
    }
    // Note: We skip URL format validation to avoid CORS issues and trust that if an image loads, it's valid

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Add a new product
  const handleAddProduct = async () => {
    if (!newProduct.name) return;

    // Validate form
    const isValid = validateProductForm();
    if (!isValid) return;

    try {
      // Automatically set is_available to false if stock is 0
      const stockQuantity = newProduct.stock_quantity || 0;
      const isAvailable = stockQuantity > 0 ? newProduct.is_available : false;

      const { error } = await supabase
        .from('products')
        .insert([
          {
            name: newProduct.name,
            description: newProduct.description || null,
            subcategory_id: newProduct.subcategory_id || null,
            price: newProduct.price,
            stock_quantity: stockQuantity,
            is_available: isAvailable,
            image_url: newProduct.image_url || null,
          },
        ]);

      if (error) throw error;

      setNewProduct({
        name: '',
        description: '',
        subcategory_id: null,
        price: 0,
        stock_quantity: 0,
        is_available: true,
        image_url: '',
      });
      setValidationErrors({});
      setIsAddingProduct(false);
      await fetchProducts();
    } catch (error: any) {
      console.error('Error adding product:', error);
      setError(error.message);
    }
  };

  // Edit an existing product
  const handleEditProduct = async () => {
    if (!newProduct.id || !newProduct.name) return;

    // Validate form
    const isValid = validateProductForm();
    if (!isValid) return;

    try {
      // Automatically set is_available to false if stock is 0
      const stockQuantity = newProduct.stock_quantity || 0;
      const isAvailable = stockQuantity > 0 ? newProduct.is_available : false;

      const { error } = await supabase
        .from('products')
        .update({
          name: newProduct.name,
          description: newProduct.description || null,
          subcategory_id: newProduct.subcategory_id || null,
          price: newProduct.price,
          stock_quantity: stockQuantity,
          is_available: isAvailable,
          image_url: newProduct.image_url || null,
          updated_at: new Date().toISOString(),
        })
        .eq('id', newProduct.id);

      if (error) throw error;

      setNewProduct({
        name: '',
        description: '',
        subcategory_id: null,
        price: 0,
        stock_quantity: 0,
        is_available: true,
        image_url: '',
      });
      setValidationErrors({});
      setIsEditingProduct(false);
      await fetchProducts();
    } catch (error: any) {
      console.error('Error updating product:', error);
      setError(error.message);
    }
  };

  // Delete a product
  const handleDeleteProduct = async (id: string) => {
    if (!confirm('Are you sure you want to delete this product?')) return;

    try {
      const { error } = await supabase
        .from('products')
        .delete()
        .eq('id', id);

      if (error) throw error;

      // Explicitly fetch products after deletion
      await fetchProducts();
    } catch (error: any) {
      console.error('Error deleting product:', error);
      setError(error.message);
    }
  };

  // Open edit dialog with product data
  const openEditDialog = (product: Product) => {
    setNewProduct({
      id: product.id,
      name: product.name,
      description: product.description,
      subcategory_id: product.subcategory_id,
      price: product.price,
      stock_quantity: product.stock_quantity,
      is_available: product.is_available,
      image_url: product.image_url,
    });
    // Set the selected category based on the product's subcategory
    if (product.subcategory?.category_id) {
      setSelectedCategory(product.subcategory.category_id);
    }
    setValidationErrors({});
    setIsEditingProduct(true);
  };

  // Handle image change with validation
  const handleImageChange = (url: string | null) => {
    setNewProduct({ ...newProduct, image_url: url });
    if (url) {
      // Clear image error when image is uploaded
      setValidationErrors(prev => ({ ...prev, image: undefined }));
    }
  };

  // Clear validation errors when dialog opens
  const openAddProductDialog = () => {
    setNewProduct({
      name: '',
      description: '',
      subcategory_id: null,
      price: 0,
      stock_quantity: 0,
      is_available: true,
      image_url: '',
    });
    setSelectedCategory(null);
    setValidationErrors({});
    setIsAddingProduct(true);
  };

  // Format price as currency
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2,
    }).format(price);
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Products</CardTitle>
            <Button onClick={openAddProductDialog}>
              <Plus className="h-4 w-4 mr-2" />
              Add Product
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search products..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9"
              />
            </div>
          </div>

          {error && (
            <div className="bg-destructive/15 text-destructive p-3 rounded-md mb-4">
              {error}
            </div>
          )}

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>ID</TableHead>
                  <TableHead>Image</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Subcategory</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Stock</TableHead>
                  <TableHead>Available</TableHead>
                  <TableHead className="w-[120px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-4">
                      Loading...
                    </TableCell>
                  </TableRow>
                ) : filteredProducts.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-4">
                      No products found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredProducts.map((product) => (
                    <TableRow key={product.id}>
                      <TableCell className="font-mono text-sm">
                        {product.product_id || '-'}
                      </TableCell>
                      <TableCell>
                        {product.image_url ? (
                          <div className="relative h-12 w-12 rounded-md overflow-hidden">
                            <img
                              src={product.image_url}
                              alt={product.name}
                              className="h-full w-full object-cover"
                              onError={(e) => {
                                (e.target as HTMLImageElement).src = 'https://placehold.co/100x100?text=No+Image';
                              }}
                            />
                          </div>
                        ) : (
                          <div className="flex h-12 w-12 items-center justify-center rounded-md bg-muted">
                            <Image className="h-6 w-6 text-muted-foreground" />
                          </div>
                        )}
                      </TableCell>
                      <TableCell className="font-medium">{product.name}</TableCell>
                      <TableCell>{product.subcategory?.name || '-'}</TableCell>
                      <TableCell>{product.subcategory?.category?.name || '-'}</TableCell>
                      <TableCell>{formatPrice(product.price)}</TableCell>
                      <TableCell>
                        <span className={product.stock_quantity === 0 ? 'text-red-500 font-medium' : ''}>
                          {product.stock_quantity}
                          {product.stock_quantity === 0 && (
                            <span className="text-xs text-red-500 block">Out of stock</span>
                          )}
                        </span>
                      </TableCell>
                      <TableCell>
                        {product.is_available ? (
                          <div className="flex items-center">
                            <Check className="h-4 w-4 text-green-500" />
                            <span className="ml-1 text-xs text-green-600">Available</span>
                          </div>
                        ) : (
                          <div className="flex items-center">
                            <X className="h-4 w-4 text-red-500" />
                            <span className="ml-1 text-xs text-red-600">
                              {product.stock_quantity === 0 ? 'No stock' : 'Unavailable'}
                            </span>
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => openEditDialog(product)}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDeleteProduct(product.id)}
                          >
                            <Trash2 className="h-4 w-4 text-destructive" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Product Form Dialog (shared between Add and Edit) */}
      <Dialog
        open={isAddingProduct || isEditingProduct}
        onOpenChange={(open) => {
          if (!open) {
            setIsAddingProduct(false);
            setIsEditingProduct(false);
            setValidationErrors({});
          }
        }}
      >
        <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {isAddingProduct ? 'Add New Product' : 'Edit Product'}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                placeholder="Enter product name"
                value={newProduct.name || ''}
                onChange={(e) => setNewProduct({ ...newProduct, name: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">Category *</Label>
              <Select
                value={selectedCategory || ''}
                onValueChange={(value) => {
                  setSelectedCategory(value || null);
                  setNewProduct({ ...newProduct, subcategory_id: null });
                  // Clear category validation error
                  setValidationErrors(prev => ({ ...prev, category: undefined, subcategory: undefined }));
                }}
              >
                <SelectTrigger className={validationErrors.category ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {validationErrors.category && (
                <p className="text-sm text-red-500">{validationErrors.category}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="subcategory">Subcategory *</Label>
              <Select
                value={newProduct.subcategory_id || ''}
                onValueChange={(value) => {
                  setNewProduct({ ...newProduct, subcategory_id: value || null });
                  // Clear subcategory validation error
                  setValidationErrors(prev => ({ ...prev, subcategory: undefined }));
                }}
                disabled={!selectedCategory}
              >
                <SelectTrigger className={validationErrors.subcategory ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select a subcategory" />
                </SelectTrigger>
                <SelectContent>
                  {subcategories
                    .filter(subcategory => !selectedCategory || subcategory.category_id === selectedCategory)
                    .map((subcategory) => (
                      <SelectItem key={subcategory.id} value={subcategory.id}>
                        {subcategory.name}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
              {validationErrors.subcategory && (
                <p className="text-sm text-red-500">{validationErrors.subcategory}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="price">Price (₹) *</Label>
              <Input
                id="price"
                type="number"
                min="0.01"
                step="0.01"
                placeholder="Enter price"
                value={newProduct.price || ''}
                onChange={(e) => {
                  setNewProduct({ ...newProduct, price: parseFloat(e.target.value) || 0 });
                  // Clear price validation error
                  setValidationErrors(prev => ({ ...prev, price: undefined }));
                }}
                className={validationErrors.price ? 'border-red-500' : ''}
              />
              {validationErrors.price && (
                <p className="text-sm text-red-500">{validationErrors.price}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="stock">Stock Quantity *</Label>
              <Input
                id="stock"
                type="number"
                min="0"
                placeholder="Enter stock quantity"
                value={newProduct.stock_quantity || ''}
                onChange={(e) => {
                  const stockQuantity = parseInt(e.target.value) || 0;
                  setNewProduct({
                    ...newProduct,
                    stock_quantity: stockQuantity,
                    // Automatically set availability to false if stock is 0
                    is_available: stockQuantity > 0 ? newProduct.is_available : false
                  });
                  // Clear stock validation error
                  setValidationErrors(prev => ({ ...prev, stock: undefined }));
                }}
                className={validationErrors.stock ? 'border-red-500' : ''}
              />
              {validationErrors.stock && (
                <p className="text-sm text-red-500">{validationErrors.stock}</p>
              )}
              {(newProduct.stock_quantity === 0) && (
                <p className="text-sm text-amber-600 flex items-center">
                  <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  Product will be automatically marked as unavailable when stock is 0
                </p>
              )}
            </div>

            <div className="space-y-2">
              <ImageUpload
                label="Product Image *"
                value={newProduct.image_url || null}
                onChange={handleImageChange}
                bucketName="product"
                folderPath="products"
              />
              {validationErrors.image && (
                <p className="text-sm text-red-500">{validationErrors.image}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description (optional)</Label>
              <Textarea
                id="description"
                placeholder="Enter product description"
                value={newProduct.description || ''}
                onChange={(e) => setNewProduct({ ...newProduct, description: e.target.value })}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="available"
                checked={newProduct.is_available}
                disabled={newProduct.stock_quantity === 0}
                onCheckedChange={(checked) => setNewProduct({ ...newProduct, is_available: checked })}
              />
              <Label htmlFor="available" className={newProduct.stock_quantity === 0 ? 'text-muted-foreground' : ''}>
                Available for purchase
                {newProduct.stock_quantity === 0 && (
                  <span className="text-xs text-amber-600 block">
                    (Disabled - No stock available)
                  </span>
                )}
              </Label>
            </div>
          </div>
          <DialogFooter className="sticky bottom-0 bg-background pt-2">
            <Button
              variant="outline"
              onClick={() => {
                setIsAddingProduct(false);
                setIsEditingProduct(false);
                setValidationErrors({});
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={isAddingProduct ? handleAddProduct : handleEditProduct}
            >
              {isAddingProduct ? 'Add Product' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}


