import * as React from 'react';
// import { ChevronRightIcon } from '@radix-ui/react-icons';
import { DayPicker } from 'react-day-picker';

import { cn } from '@/lib/utils';
// import { buttonVariants } from '@/components/ui/button';
import './calendar.css';

export type CalendarProps = React.ComponentProps<typeof DayPicker>;

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  ...props
}: CalendarProps) {
  return (
    <DayPicker
      showOutsideDays={showOutsideDays}
      className={cn('', className)}
      classNames={{
        ...classNames,
      }}
      components={{
        // IconLeft: () => <ChevronLeftIcon className="h-5 w-5" />,
        // IconRight: () => <ChevronRightIcon className="h-5 w-5" />,
      }}
      {...props}
    />
  );
}
Calendar.displayName = 'Calendar';

export { Calendar };
