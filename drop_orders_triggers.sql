-- SQL script to drop all triggers on the orders table

-- First, get a list of all triggers on the orders table
DO $$
DECLARE
    trigger_record RECORD;
BEGIN
    FOR trigger_record IN 
        SELECT tgname AS trigger_name
        FROM pg_trigger
        WHERE tgrelid = 'orders'::regclass
    LOOP
        EXECUTE format('DROP TRIGGER IF EXISTS %I ON orders', trigger_record.trigger_name);
        RAISE NOTICE 'Dropped trigger: %', trigger_record.trigger_name;
    END LOOP;
END $$;

-- Verify no triggers remain
SELECT tgname FROM pg_trigger WHERE tgrelid = 'orders'::regclass;
