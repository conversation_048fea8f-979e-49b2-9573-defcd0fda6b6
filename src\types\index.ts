export type OrderStatus = 'New' | 'Processing' | 'Out for Delivery' | 'Delivered' | 'Cancelled';
export type PaymentMethod = 'COD' | 'ONLINE';
export type PaymentStatus = 'Pending' | 'Completed' | 'Failed' | 'Refunded';
export type ReturnStatus = 'Requested' | 'Approved' | 'Rejected' | 'Returned';
export type UserRole = 'Super Admin' | 'Admin';

export interface User {
  id: string;
  name: string;
  phone: string;
  email: string;
  password?: string;
  role: UserRole;
  created_at: string;
}

export interface Customer {
  id: string;
  name: string;
  whatsapp_number: string;
  email?: string;
  created_at: string;
}

export interface Order {
  id: string;
  customer_id: string;
  total_amount: number;
  status: OrderStatus;
  payment_method: PaymentMethod;
  payment_status?: string;
  address_id?: string;
  razorpay_payment_id?: string;
  payment_auth_date?: string;
  payment_capture_date?: string;
  payment_failure_date?: string;
  payment_error?: string;
  payment_error_code?: string;
  cancellation_reason?: string;
  created_at: string;
  updated_at: string;
}

export interface OrderItem {
  id: string;
  order_id: string;
  product_name: string;
  product_description?: string;
  product_image?: string;
  quantity: number;
  price: number;
  cancellation_reason?: string;
}

export interface Address {
  id: string;
  customer_id: string;
  address: string;
  pincode: string;
  is_default?: boolean;
}

export interface Payment {
  id: string;
  order_id: string;
  customer_id: string;
  amount: number;
  payment_method: PaymentMethod;
  payment_status: PaymentStatus;
  transaction_id?: string;
  cancellation_reason?: string;
  created_at: string;
  updated_at: string;
}

export interface ReturnProduct {
  id: string;
  order_id: string;
  product_name: string;
  quantity: number;
  return_reason: string;
  status: ReturnStatus;
  requested_at: string;
  processed_at?: string;
  processed_by?: string;
}

export interface ReturnRequest {
  id: string;
  orderId: string;
  customerName: string;
  customerPhone: string;
  items: OrderItem[];
  reason: string;
  status: ReturnStatus;
  requestDate: string;
  processedAt?: string;
  processedBy?: string;
  rejectionReason?: string;
  returnedDate?: string;
}

export interface DashboardMetrics {
  totalOrders: number;
  pendingOrders: number;
  completedOrders: number;
  cancelledOrders: number;
  newCustomers: number;
  returningCustomers: number;
  revenue: number;
}
