import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from '@/components/ui/dialog';
import { Plus, Trash2, UserPlus2 } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface DeliveryArea {
  pincode: string;
  charge: number;
}

interface Admin {
  name: string;
  phone: string;
  email: string;
  role: string;
}

export function Settings() {
  const [deliveryAreas, setDeliveryAreas] = useState<DeliveryArea[]>([
    { pincode: '400001', charge: 40 },
    { pincode: '400002', charge: 50 },
    { pincode: '400003', charge: 60 },
  ]);
  const [admins, setAdmins] = useState<Admin[]>([
    { name: 'Admin User', phone: '+91 98765 43210', email: '<EMAIL>', role: 'Super Admin' },
  ]);
  const [isAddingAdmin, setIsAddingAdmin] = useState(false);
  const [newAdmin, setNewAdmin] = useState<Admin>({ name: '', phone: '', email: '', role: 'Admin' });
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [isAddingDeliveryArea, setIsAddingDeliveryArea] = useState(false);
  const [newDeliveryArea, setNewDeliveryArea] = useState<DeliveryArea>({ pincode: '', charge: 0 });

  const handleAddDeliveryArea = () => {
    if (newDeliveryArea.pincode && newDeliveryArea.charge > 0) {
      setDeliveryAreas([...deliveryAreas, newDeliveryArea]);
      setNewDeliveryArea({ pincode: '', charge: 0 });
      setIsAddingDeliveryArea(false);
    }
  };

  const handleRemoveDeliveryArea = (index: number) => {
    setDeliveryAreas(deliveryAreas.filter((_, i) => i !== index));
  };

  const handleAddAdmin = () => {
    if (newAdmin.name && newAdmin.phone && newAdmin.email) {
      setAdmins([...admins, newAdmin]);
      setNewAdmin({ name: '', phone: '', email: '', role: 'Admin' });
      setIsAddingAdmin(false);
    }
  };

  const handleRemoveAdmin = (index: number) => {
    setAdmins(admins.filter((_, i) => i !== index));
  };

  return (
    <div className="space-y-6">
      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Admin Management</CardTitle>
              <Dialog open={isAddingAdmin} onOpenChange={setIsAddingAdmin}>
                <DialogTrigger asChild>
                  <Button variant="default">
                    <UserPlus2 className="h-4 w-4 mr-2" />
                    Add Admin
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add New Admin</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4 py-4">
                    <div className="space-y-2">
                      <Label>Name</Label>
                      <Input
                        placeholder="Enter admin name"
                        value={newAdmin.name}
                        onChange={(e) => setNewAdmin({ ...newAdmin, name: e.target.value })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Phone</Label>
                      <Input
                        placeholder="+91 XXXXX XXXXX"
                        value={newAdmin.phone}
                        onChange={(e) => setNewAdmin({ ...newAdmin, phone: e.target.value })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Email</Label>
                      <Input
                        type="email"
                        placeholder="<EMAIL>"
                        value={newAdmin.email}
                        onChange={(e) => setNewAdmin({ ...newAdmin, email: e.target.value })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Role</Label>
                      <Select
                        value={newAdmin.role}
                        onValueChange={(value) => setNewAdmin({ ...newAdmin, role: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select role" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Admin">Admin</SelectItem>
                          <SelectItem value="Manager">Manager</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsAddingAdmin(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleAddAdmin}>Add Admin</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Phone</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead className="w-[100px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {admins.map((admin, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{admin.name}</TableCell>
                      <TableCell>{admin.phone}</TableCell>
                      <TableCell>{admin.email}</TableCell>
                      <TableCell>{admin.role}</TableCell>
                      <TableCell>
                        {admin.role !== 'Super Admin' && (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleRemoveAdmin(index)}
                            className="text-destructive hover:text-destructive/90"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Delivery Areas</CardTitle>
              <Dialog open={isAddingDeliveryArea} onOpenChange={setIsAddingDeliveryArea}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Area
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add New Delivery Area</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4 py-4">
                    <div className="space-y-2">
                      <Label>Pincode</Label>
                      <Input
                        placeholder="Enter pincode"
                        value={newDeliveryArea.pincode}
                        onChange={(e) => setNewDeliveryArea({ ...newDeliveryArea, pincode: e.target.value })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Delivery Charge (₹)</Label>
                      <Input
                        type="number"
                        placeholder="Enter delivery charge"
                        value={newDeliveryArea.charge || ''}
                        onChange={(e) => setNewDeliveryArea({ ...newDeliveryArea, charge: parseInt(e.target.value) })}
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsAddingDeliveryArea(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleAddDeliveryArea}>Add Area</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Pincode</TableHead>
                    <TableHead>Delivery Charge (₹)</TableHead>
                    <TableHead className="w-[100px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {deliveryAreas.map((area, index) => (
                    <TableRow key={index}>
                      <TableCell>{area.pincode}</TableCell>
                      <TableCell>₹{area.charge}</TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleRemoveDeliveryArea(index)}
                          className="text-destructive hover:text-destructive/90"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Change Password</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Current Password</Label>
              <Input
                type="password"
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label>New Password</Label>
              <Input
                type="password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
              />
            </div>
            <Button>Update Password</Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}