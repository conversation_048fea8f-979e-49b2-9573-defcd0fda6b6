import { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Customers } from '@/pages/customers';
import { Orders } from '@/pages/orders';
import { Categories } from '@/pages/categories';
import { Products } from '@/pages/products';

interface DashboardRoutesProps {
  onLogout: () => void;
}

export function DashboardRoutes({ onLogout }: DashboardRoutesProps) {
  const [currentPath, setCurrentPath] = useState(window.location.pathname);

  useEffect(() => {
    const handleLocationChange = () => {
      setCurrentPath(window.location.pathname);
    };

    window.addEventListener('popstate', handleLocationChange);
    return () => window.removeEventListener('popstate', handleLocationChange);
  }, []);

  const renderContent = () => {
    switch (currentPath) {
      case '/customers':
        return <Customers />;
      case '/orders':
        return <Orders />;
      case '/categories':
        return <Categories />;
      case '/products':
        return <Products />;
      default:
        return <Orders />;
    }
  };

  return <DashboardLayout onLogout={onLogout}>{renderContent()}</DashboardLayout>;
}
