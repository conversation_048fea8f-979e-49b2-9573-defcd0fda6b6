-- SQL trigger to automatically set is_available to false when stock_quantity is 0
-- This ensures data consistency at the database level

-- Create or replace the trigger function
CREATE OR REPLACE FUNCTION update_product_availability()
RETURNS TRIGGER AS $$
BEGIN
    -- If stock_quantity is 0, automatically set is_available to false
    IF NEW.stock_quantity = 0 THEN
        NEW.is_available = false;
    END IF;
    
    -- Update the updated_at timestamp
    NEW.updated_at = NOW();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop the trigger if it exists
DROP TRIGGER IF EXISTS trigger_update_product_availability ON products;

-- Create the trigger
CREATE TRIGGER trigger_update_product_availability
    BEFORE INSERT OR UPDATE ON products
    FOR EACH ROW
    EXECUTE FUNCTION update_product_availability();

-- Update existing products with 0 stock to be unavailable
UPDATE products 
SET is_available = false, updated_at = NOW() 
WHERE stock_quantity = 0 AND is_available = true;

-- Verify the changes
SELECT 
    id, 
    name, 
    stock_quantity, 
    is_available,
    updated_at
FROM products 
WHERE stock_quantity = 0
ORDER BY name;
