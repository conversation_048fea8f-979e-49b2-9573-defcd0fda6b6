import { Customer, Order, DashboardMetrics, ReturnRequest } from '@/types';

export const mockCustomers: Customer[] = [
  {
    id: '1',
    name: '<PERSON>',
    whatsapp_number: '+91 98765 43210',
    email: '<EMAIL>',
    created_at: '2024-03-20T10:30:00Z',
  },
  // Add more mock customers...
];

export const mockOrders: Order[] = [
  {
    id: 'ORD001',
    customer_id: '1',
    total_amount: 250,
    status: 'New',
    payment_method: 'COD',
    created_at: '2024-03-20T10:30:00Z',
    updated_at: '2024-03-20T10:30:00Z',
  },
  // Add more mock orders...
];

export const mockReturns: ReturnRequest[] = [
  {
    id: 'RET001',
    orderId: 'ORD001',
    customerName: '<PERSON>',
    customerPhone: '+91 98765 43210',
    items: [
      { id: '1', order_id: 'ORD001', product_name: 'Rice', quantity: 1, price: 100 },
    ],
    reason: 'Product damaged during delivery',
    status: 'Requested',
    requestDate: '2024-03-21T10:30:00Z',
  },
  {
    id: 'RET002',
    orderId: 'ORD002',
    customerName: 'Jane <PERSON>',
    customerPhone: '+91 98765 43211',
    items: [
      { id: '3', order_id: 'ORD002', product_name: 'Sugar', quantity: 2, price: 45 },
    ],
    reason: 'Wrong product delivered',
    status: 'Approved',
    requestDate: '2024-03-20T15:45:00Z',
    // adminComments: 'Approved for return',
  },
  {
    id: 'RET003',
    orderId: 'ORD003',
    customerName: 'Alice Johnson',
    customerPhone: '+91 98765 43212',
    items: [
      { id: '4', order_id: 'ORD003', product_name: 'Oil', quantity: 1, price: 150 },
    ],
    reason: 'Quality issues',
    status: 'Rejected',
    requestDate: '2024-03-19T09:15:00Z',
    rejectionReason: 'Product was used',
  },
  {
    id: 'RET004',
    orderId: 'ORD004',
    customerName: 'Bob Wilson',
    customerPhone: '+91 98765 43213',
    items: [
      { id: '5', order_id: 'ORD004', product_name: 'Wheat Flour', quantity: 2, price: 80 },
    ],
    reason: 'Changed mind',
    status: 'Returned',
    requestDate: '2024-03-18T14:20:00Z',
    returnedDate: '2024-03-20T16:30:00Z',
  },
];

export const mockMetrics: DashboardMetrics = {
  totalOrders: 100,
  pendingOrders: 25,
  completedOrders: 70,
  cancelledOrders: 5,
  newCustomers: 30,
  returningCustomers: 70,
  revenue: 25000,
};