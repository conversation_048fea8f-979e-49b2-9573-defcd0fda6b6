// ✅ Updated Customers Page with live data
// Assuming you have totalOrders, lastOrderDate, and joinedDate available in customerDetails

import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Search, MapPin, Phone, Calendar } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { supabase } from '@/lib/supabase.config';
import { Customer, Address, Order } from '@/types';

type CustomerDetails = Customer & {
  totalOrders: number;
  lastOrderDate: string | null;
  joinedDate: string;
};

export function Customers() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState<string | null>(null);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [customerDetails, setCustomerDetails] = useState<CustomerDetails | null>(null);

  useEffect(() => {
    async function getCustomers() {
      const { data, error } = await supabase
        .from('customers')
        .select('*')
        .returns<Customer[]>();
      if (!error) setCustomers(data || []);
    }

    getCustomers();

    const channel = supabase
      .channel('customers-realtime')
      .on('postgres_changes', { 
        event: '*', 
        schema: 'public', 
        table: 'customers' 
      }, () => {
        getCustomers();
        return Promise.resolve();
      })
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  useEffect(() => {
    async function getCustomerDetails() {
      if (!selectedCustomer) return;
      
      const [ordersRes, addrRes, custRes] = await Promise.all([
        supabase
          .from('orders')
          .select('*')
          .eq('customer_id', selectedCustomer)
          .returns<Order[]>(),
        supabase
          .from('addresses')
          .select('*')
          .eq('customer_id', selectedCustomer)
          .returns<Address[]>(),
        supabase
          .from('customers')
          .select('*')
          .eq('id', selectedCustomer)
          .single<Customer>(),
      ]);

      setAddresses(addrRes.data || []);
      const orders = ordersRes.data || [];
      const lastOrderDate = orders.length ? orders[orders.length - 1].created_at : null;
      
      if (custRes.data) {
        setCustomerDetails({
          ...custRes.data,
          totalOrders: orders.length,
          lastOrderDate,
          joinedDate: custRes.data.created_at,
        });
      }
    }

    getCustomerDetails();
  }, [selectedCustomer]);

  const filteredCustomers = customers.filter(
    (customer) =>
      customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.whatsapp_number.includes(searchTerm)
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Customers</h2>
          <p className="text-muted-foreground">Manage and view customer information</p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Customer List</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="relative mb-6">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search customers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9"
            />
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Customer</TableHead>
                  <TableHead>Phone</TableHead>
                  <TableHead>Orders</TableHead>
                  <TableHead>Last Order</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredCustomers.map((customer) => (
                  <TableRow key={customer.id}>
                    <TableCell className="font-medium">{customer.name}</TableCell>
                    <TableCell>{customer.whatsapp_number}</TableCell>
                    <TableCell>
                      {
                        customer.id === customerDetails?.id
                          ? customerDetails?.totalOrders
                          : '—'
                      }
                    </TableCell>
                    <TableCell>
                      {
                        customer.id === customerDetails?.id && customerDetails?.lastOrderDate
                          ? formatDistanceToNow(new Date(customerDetails.lastOrderDate), { addSuffix: true })
                          : '—'
                      }
                    </TableCell>
                    <TableCell>
                      <Button variant="ghost" onClick={() => setSelectedCustomer(customer.id)}>
                        View Details
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <Dialog open={selectedCustomer !== null} onOpenChange={() => setSelectedCustomer(null)}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Customer Details</DialogTitle>
          </DialogHeader>
          {customerDetails && (
            <div className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="h-16 w-16 rounded-full bg-blue-100 flex items-center justify-center">
                    <span className="text-2xl font-semibold text-blue-600">
                      {customerDetails.name?.split(' ').map((n: string) => n[0]).join('')}
                    </span>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold">{customerDetails.name || 'N/A'}</h3>
                    <div className="flex items-center text-muted-foreground">
                      <Phone className="h-4 w-4 mr-2" />
                      {customerDetails.whatsapp_number || 'N/A'}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-2xl font-bold">{customerDetails.totalOrders}</div>
                      <p className="text-sm text-muted-foreground">Total Orders</p>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-2xl font-bold">
                        {customerDetails.lastOrderDate
                          ? formatDistanceToNow(new Date(customerDetails.lastOrderDate), { addSuffix: true })
                          : 'N/A'}
                      </div>
                      <p className="text-sm text-muted-foreground">Last Order</p>
                    </CardContent>
                  </Card>
                </div>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm font-medium">Delivery Addresses</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {addresses.length > 0 ? (
                        addresses.map((addr, idx) => (
                          <div key={idx} className="text-sm">
                            <MapPin className="inline-block h-4 w-4 mr-1" />
                            {addr.address}, {addr.pincode}
                          </div>
                        ))
                      ) : (
                        <div>No addresses</div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm font-medium">Recent Activity</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center space-x-2 text-sm">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span>
                          Joined{' '}
                          {customerDetails.joinedDate
                            ? formatDistanceToNow(new Date(customerDetails.joinedDate), { addSuffix: true })
                            : 'N/A'}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
