import { useState, useEffect } from 'react';
import { LoginPage } from '@/pages/login';
import { DashboardRoutes } from '@/pages/dashboard-routes';
import { UpdatePassword } from '@/pages/update-password';
import { supabase } from '@/lib/supabase.config';

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check for existing session on load
    supabase.auth.getSession().then(({ data: { session } }) => {
      setIsAuthenticated(!!session);
      setLoading(false);
    });

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setIsAuthenticated(!!session);
    });

    return () => subscription.unsubscribe();
  }, []);

  // Check if we're on the update password route
  const isUpdatePasswordRoute = window.location.pathname === '/update-password';

  if (loading) {
    return <div className="fixed inset-0 flex items-center justify-center">Loading...</div>;
  }

  // If we're on the update password route, show the update password page
  if (isUpdatePasswordRoute) {
    return <UpdatePassword />;
  }

  if (!isAuthenticated) {
    return <LoginPage onLogin={() => setIsAuthenticated(true)} />;
  }

  return <DashboardRoutes onLogout={() => supabase.auth.signOut()} />;
}

export default App;
