import React, { useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  addMonths,
  subMonths,
  format,
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  isSameMonth,
  isSameDay,
  // addDays,
  startOfWeek,
  endOfWeek
} from 'date-fns';

interface CalendarProps {
  value?: Date | null;
  onChange?: (date: Date) => void;
  className?: string;
}

const DAYS_OF_WEEK = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];

export function CustomCalendar({ value, onChange, className }: CalendarProps) {
  const [currentMonth, setCurrentMonth] = useState(value || new Date());

  const handlePreviousMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1));
  };

  const handleNextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1));
  };

  const handleDateClick = (day: Date) => {
    if (onChange) {
      onChange(day);
    }
  };

  // Get days for the calendar grid
  const monthStart = startOfMonth(currentMonth);
  const monthEnd = endOfMonth(currentMonth);
  const startDate = startOfWeek(monthStart);
  const endDate = endOfWeek(monthEnd);

  const days = eachDayOfInterval({ start: startDate, end: endDate });

  // Group days into weeks
  const weeks: Date[][] = [];
  let week: Date[] = [];

  days.forEach((day) => {
    if (week.length === 7) {
      weeks.push(week);
      week = [];
    }
    week.push(day);
  });

  if (week.length > 0) {
    weeks.push(week);
  }

  return (
    <div className={`p-4 bg-white rounded-lg shadow ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold">{format(currentMonth, 'MMMM yyyy')}</h2>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="icon"
            onClick={handlePreviousMonth}
            className="h-8 w-8 p-0"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={handleNextMonth}
            className="h-8 w-8 p-0"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-7 gap-1">
        {DAYS_OF_WEEK.map((day) => (
          <div
            key={day}
            className="text-center text-sm font-medium text-gray-500 py-2"
          >
            {day}
          </div>
        ))}

        {weeks.map((week, weekIndex) => (
          <React.Fragment key={weekIndex}>
            {week.map((day) => {
              const isCurrentMonth = isSameMonth(day, currentMonth);
              const isSelected = value ? isSameDay(day, value) : false;

              return (
                <button
                  key={day.toString()}
                  type="button"
                  onClick={() => handleDateClick(day)}
                  className={`
                    h-10 w-full rounded-md text-center text-sm
                    ${isCurrentMonth ? 'text-gray-900' : 'text-gray-400'}
                    ${isSelected ? 'bg-primary text-primary-foreground' : 'hover:bg-gray-100'}
                  `}
                >
                  {format(day, 'd')}
                </button>
              );
            })}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
}

interface DateRangeCalendarProps {
  value?: { from: Date | null; to: Date | null };
  onChange?: (range: { from: Date | null; to: Date | null }) => void;
  className?: string;
}

export function CustomDateRangeCalendar({ value, onChange, className }: DateRangeCalendarProps) {
  const [currentMonth, setCurrentMonth] = useState(value?.from || new Date());
  const [hoverDate, setHoverDate] = useState<Date | null>(null);

  const handlePreviousMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1));
  };

  const handleNextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1));
  };

  const handleDateClick = (day: Date) => {
    if (!onChange) return;

    if (!value?.from) {
      onChange({ from: day, to: null });
    } else if (value.from && !value.to) {
      // If clicking a day before the start date, swap them
      if (day < value.from) {
        onChange({ from: day, to: value.from });
      } else {
        onChange({ from: value.from, to: day });
      }
    } else {
      // Reset and start new selection
      onChange({ from: day, to: null });
    }
  };

  const handleDateHover = (day: Date) => {
    setHoverDate(day);
  };

  // Get days for the calendar grid
  const monthStart = startOfMonth(currentMonth);
  const monthEnd = endOfMonth(currentMonth);
  const startDate = startOfWeek(monthStart);
  const endDate = endOfWeek(monthEnd);

  const days = eachDayOfInterval({ start: startDate, end: endDate });

  // Group days into weeks
  const weeks: Date[][] = [];
  let week: Date[] = [];

  days.forEach((day) => {
    if (week.length === 7) {
      weeks.push(week);
      week = [];
    }
    week.push(day);
  });

  if (week.length > 0) {
    weeks.push(week);
  }

  // Function to check if a date is in the selected range
  const isInRange = (day: Date) => {
    if (!value?.from) return false;
    if (value.from && !value.to) {
      if (hoverDate) {
        return (
          (day >= value.from && day <= hoverDate) ||
          (day <= value.from && day >= hoverDate)
        );
      }
      return false;
    }
    return value.to ? day >= value.from && day <= value.to : false;
  };

  return (
    <div className={`p-4 bg-white rounded-lg shadow ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold">{format(currentMonth, 'MMMM yyyy')}</h2>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="icon"
            onClick={handlePreviousMonth}
            className="h-8 w-8 p-0"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={handleNextMonth}
            className="h-8 w-8 p-0"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-7 gap-1">
        {DAYS_OF_WEEK.map((day) => (
          <div
            key={day}
            className="text-center text-sm font-medium text-gray-500 py-2"
          >
            {day}
          </div>
        ))}

        {weeks.map((week, weekIndex) => (
          <React.Fragment key={weekIndex}>
            {week.map((day) => {
              const isCurrentMonth = isSameMonth(day, currentMonth);
              const isStart = value?.from ? isSameDay(day, value.from) : false;
              const isEnd = value?.to ? isSameDay(day, value.to) : false;
              const isRangeDate = isInRange(day);

              return (
                <button
                  key={day.toString()}
                  type="button"
                  onClick={() => handleDateClick(day)}
                  onMouseEnter={() => handleDateHover(day)}
                  className={`
                    h-10 w-full rounded-md text-center text-sm relative
                    ${isCurrentMonth ? 'text-gray-900' : 'text-gray-400'}
                    ${isStart || isEnd ? 'bg-primary text-primary-foreground z-10' : ''}
                    ${isRangeDate && !isStart && !isEnd ? 'bg-primary/10' : ''}
                    ${!isStart && !isEnd && !isRangeDate ? 'hover:bg-gray-100' : ''}
                  `}
                >
                  {format(day, 'd')}
                </button>
              );
            })}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
}

export function CustomDateRangePicker({
  value,
  onChange,
  className
}: DateRangeCalendarProps) {
  const [showSecondMonth, setShowSecondMonth] = useState(false);
  // const [firstMonth, setFirstMonth] = useState(value?.from || new Date());

  // const handlePreviousMonth = () => {
  //   setFirstMonth(subMonths(firstMonth, 1));
  // };

  // const handleNextMonth = () => {
  //   setFirstMonth(addMonths(firstMonth, 1));
  // };

  return (
    <div className={`${className}`}>
      <div className="flex flex-col md:flex-row gap-4">
        <CustomDateRangeCalendar
          value={value}
          onChange={onChange}
          className="w-full"
        />

        {showSecondMonth && (
          <CustomDateRangeCalendar
            value={value}
            onChange={onChange}
            className="w-full"
          />
        )}
      </div>

      <div className="mt-4 flex justify-end">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowSecondMonth(!showSecondMonth)}
        >
          {showSecondMonth ? 'Show Single Month' : 'Show Two Months'}
        </Button>
      </div>
    </div>
  );
}
