import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Search, Plus, Pencil, Trash2, Image, Layers } from 'lucide-react';
import { supabase } from '@/lib/supabase.config';
import { ImageUpload } from '@/components/ui/image-upload';

interface Category {
  id: string;
  name: string;
  image_url: string | null;
  created_at: string;
}

interface Subcategory {
  id: string;
  category_id: string;
  name: string;
  image_url: string | null;
  created_at: string;
}

export function Categories() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [subcategories, setSubcategories] = useState<Subcategory[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isAddingCategory, setIsAddingCategory] = useState(false);
  const [isEditingCategory, setIsEditingCategory] = useState(false);
  const [isAddingSubcategory, setIsAddingSubcategory] = useState(false);
  const [isEditingSubcategory, setIsEditingSubcategory] = useState(false);
  const [isViewingSubcategories, setIsViewingSubcategories] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [newCategory, setNewCategory] = useState<Partial<Category>>({
    name: '',
    image_url: '',
  });
  const [newSubcategory, setNewSubcategory] = useState<Partial<Subcategory>>({
    name: '',
    image_url: '',
    category_id: '',
  });
  const [loading, setLoading] = useState(true);
  const [subcategoriesLoading, setSubcategoriesLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [categoryImageError, setCategoryImageError] = useState<string | null>(null);
  const [subcategoryImageError, setSubcategoryImageError] = useState<string | null>(null);

  // Fetch categories from Supabase
  const fetchCategories = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .order('name');

      if (error) {
        throw error;
      }

      setCategories(data || []);
    } catch (error: any) {
      setError(error.message);
      console.error('Error fetching categories:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch subcategories for a specific category
  const fetchSubcategories = async (categoryId: string) => {
    setSubcategoriesLoading(true);
    try {
      const { data, error } = await supabase
        .from('subcategories')
        .select('*')
        .eq('category_id', categoryId)
        .order('name');

      if (error) {
        throw error;
      }

      setSubcategories(data || []);
    } catch (error: any) {
      setError(error.message);
      console.error('Error fetching subcategories:', error);
    } finally {
      setSubcategoriesLoading(false);
    }
  };

  // Add a new subcategory
  const handleAddSubcategory = async () => {
    if (!newSubcategory.name || !newSubcategory.category_id) return;

    // Validate image is provided
    if (!newSubcategory.image_url) {
      setSubcategoryImageError('Photo upload is mandatory. Please upload an image or provide an image URL.');
      return;
    }

    // Validate image URL format
    const isValidImage = validateImageUrl(newSubcategory.image_url);
    if (!isValidImage) {
      setSubcategoryImageError('Invalid image URL. Please provide a valid image URL or upload an image.');
      return;
    }

    setSubcategoryImageError(null); // Clear any previous error

    try {
      const { error } = await supabase
        .from('subcategories')
        .insert([
          {
            name: newSubcategory.name,
            category_id: newSubcategory.category_id,
            image_url: newSubcategory.image_url || null,
          },
        ]);

      if (error) throw error;

      setNewSubcategory({ name: '', image_url: '', category_id: newSubcategory.category_id });
      setIsAddingSubcategory(false);
      setSubcategoryImageError(null); // Clear error on success
      fetchSubcategories(newSubcategory.category_id);
    } catch (error: any) {
      console.error('Error adding subcategory:', error);
      setError(error.message);
    }
  };

  // Edit an existing subcategory
  const handleEditSubcategory = async () => {
    if (!newSubcategory.id || !newSubcategory.name || !newSubcategory.category_id) return;

    // Validate image is provided
    if (!newSubcategory.image_url) {
      setSubcategoryImageError('Photo upload is mandatory. Please upload an image or provide an image URL.');
      return;
    }

    // Validate image URL format
    const isValidImage = validateImageUrl(newSubcategory.image_url);
    if (!isValidImage) {
      setSubcategoryImageError('Invalid image URL. Please provide a valid image URL or upload an image.');
      return;
    }

    setSubcategoryImageError(null); // Clear any previous error

    try {
      const { error } = await supabase
        .from('subcategories')
        .update({
          name: newSubcategory.name,
          image_url: newSubcategory.image_url || null,
        })
        .eq('id', newSubcategory.id);

      if (error) throw error;

      setNewSubcategory({ name: '', image_url: '', category_id: newSubcategory.category_id });
      setIsEditingSubcategory(false);
      setSubcategoryImageError(null); // Clear error on success
      fetchSubcategories(newSubcategory.category_id);
    } catch (error: any) {
      console.error('Error updating subcategory:', error);
      setError(error.message);
    }
  };

  // Delete a subcategory
  const handleDeleteSubcategory = async (id: string, categoryId: string) => {
    if (!confirm('Are you sure you want to delete this subcategory?')) return;

    try {
      const { error } = await supabase
        .from('subcategories')
        .delete()
        .eq('id', id);

      if (error) throw error;

      fetchSubcategories(categoryId);
    } catch (error: any) {
      console.error('Error deleting subcategory:', error);
      setError(error.message);
    }
  };

  // Open subcategories dialog for a category
  const openSubcategoriesDialog = (category: Category) => {
    setSelectedCategory(category);
    fetchSubcategories(category.id);
    setIsViewingSubcategories(true);
  };

  // Open add subcategory dialog
  const openAddSubcategoryDialog = () => {
    if (!selectedCategory) return;

    setNewSubcategory({
      name: '',
      image_url: '',
      category_id: selectedCategory.id,
    });
    setSubcategoryImageError(null); // Clear any previous error
    setIsAddingSubcategory(true);
  };

  // Open edit subcategory dialog
  const openEditSubcategoryDialog = (subcategory: Subcategory) => {
    setNewSubcategory({
      id: subcategory.id,
      name: subcategory.name,
      image_url: subcategory.image_url,
      category_id: subcategory.category_id,
    });
    setSubcategoryImageError(null); // Clear any previous error
    setIsEditingSubcategory(true);
  };

  // Initial fetch and realtime subscription
  useEffect(() => {
    fetchCategories();

    const categoriesChannel = supabase
      .channel('categories-realtime')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'categories' },
        (payload) => {
          console.log('Categories change detected:', payload);
          fetchCategories();
          return Promise.resolve();
        }
      )
      .subscribe();

    const subcategoriesChannel = supabase
      .channel('subcategories-realtime')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'subcategories' },
        (payload) => {
          console.log('Subcategories change detected:', payload);
          if (selectedCategory) {
            fetchSubcategories(selectedCategory.id);
          }
          return Promise.resolve();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(categoriesChannel);
      supabase.removeChannel(subcategoriesChannel);
    };
  }, []);

  // Handle selectedCategory changes
  useEffect(() => {
    if (selectedCategory) {
      fetchSubcategories(selectedCategory.id);
    }
  }, [selectedCategory]);

  // Filter categories based on search term
  const filteredCategories = categories.filter((category) =>
    category.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Validate image URL
  const validateImageUrl = (url: string): boolean => {
    try {
      // Check if it's a valid URL
      const urlObj = new URL(url);
      // Must be http or https - if it is, we'll accept it as a valid image URL
      return ['http:', 'https:'].includes(urlObj.protocol);
    } catch {
      return false;
    }
  };

  // Add a new category
  const handleAddCategory = async () => {
    if (!newCategory.name) return;

    // Validate image is provided
    if (!newCategory.image_url) {
      setCategoryImageError('Photo upload is mandatory. Please upload an image or provide an image URL.');
      return;
    }

    // Validate image URL format
    const isValidImage = validateImageUrl(newCategory.image_url);
    if (!isValidImage) {
      setCategoryImageError('Invalid image URL. Please provide a valid image URL or upload an image.');
      return;
    }

    setCategoryImageError(null); // Clear any previous error

    try {
      const { error } = await supabase
        .from('categories')
        .insert([
          {
            name: newCategory.name,
            image_url: newCategory.image_url || null,
          },
        ]);

      if (error) throw error;

      setNewCategory({ name: '', image_url: '' });
      setIsAddingCategory(false);
      setCategoryImageError(null); // Clear error on success

      // Explicitly fetch categories after adding a new one
      await fetchCategories();
    } catch (error: any) {
      console.error('Error adding category:', error);
      setError(error.message);
    }
  };

  // Edit an existing category
  const handleEditCategory = async () => {
    if (!newCategory.id || !newCategory.name) return;

    // Validate image is provided
    if (!newCategory.image_url) {
      setCategoryImageError('Photo upload is mandatory. Please upload an image or provide an image URL.');
      return;
    }

    // Validate image URL format
    const isValidImage = validateImageUrl(newCategory.image_url);
    if (!isValidImage) {
      setCategoryImageError('Invalid image URL. Please provide a valid image URL or upload an image.');
      return;
    }

    setCategoryImageError(null); // Clear any previous error

    try {
      const { error } = await supabase
        .from('categories')
        .update({
          name: newCategory.name,
          image_url: newCategory.image_url || null,
        })
        .eq('id', newCategory.id);

      if (error) throw error;

      setNewCategory({ name: '', image_url: '' });
      setIsEditingCategory(false);
      setCategoryImageError(null); // Clear error on success

      // Explicitly fetch categories after editing
      await fetchCategories();
    } catch (error: any) {
      console.error('Error updating category:', error);
      setError(error.message);
    }
  };

  // Delete a category
  const handleDeleteCategory = async (id: string) => {
    if (!confirm('Are you sure you want to delete this category?')) return;

    try {
      const { error } = await supabase
        .from('categories')
        .delete()
        .eq('id', id);

      if (error) throw error;

      // Explicitly fetch categories after deleting
      await fetchCategories();
    } catch (error: any) {
      console.error('Error deleting category:', error);
      setError(error.message);
    }
  };

  // Open edit dialog with category data
  const openEditDialog = (category: Category) => {
    setNewCategory({
      id: category.id,
      name: category.name,
      image_url: category.image_url,
    });
    setCategoryImageError(null); // Clear any previous error
    setIsEditingCategory(true);
  };

  // Clear category image error when image is uploaded
  const handleCategoryImageChange = (url: string | null) => {
    setNewCategory({ ...newCategory, image_url: url });
    if (url) {
      setCategoryImageError(null); // Clear error when image is uploaded

      // Validate URL if it looks like a URL (contains http/https)
      if (url.startsWith('http')) {
        const isValid = validateImageUrl(url);
        if (!isValid) {
          setCategoryImageError('Invalid image URL. Please provide a valid image URL.');
        }
      }
    }
  };

  // Clear subcategory image error when image is uploaded
  const handleSubcategoryImageChange = (url: string | null) => {
    setNewSubcategory({ ...newSubcategory, image_url: url });
    if (url) {
      setSubcategoryImageError(null); // Clear error when image is uploaded

      // Validate URL if it looks like a URL (contains http/https)
      if (url.startsWith('http')) {
        const isValid = validateImageUrl(url);
        if (!isValid) {
          setSubcategoryImageError('Invalid image URL. Please provide a valid image URL.');
        }
      }
    }
  };

  // Open add category dialog
  const openAddCategoryDialog = () => {
    setNewCategory({ name: '', image_url: '' });
    setCategoryImageError(null); // Clear any previous error
    setIsAddingCategory(true);
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Categories</CardTitle>
            <Button onClick={openAddCategoryDialog}>
              <Plus className="h-4 w-4 mr-2" />
              Add Category
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search categories..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9"
              />
            </div>
          </div>

          {error && (
            <div className="bg-destructive/15 text-destructive p-3 rounded-md mb-4">
              {error}
            </div>
          )}

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Image</TableHead>
                  <TableHead className="w-[180px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center py-4">
                      Loading...
                    </TableCell>
                  </TableRow>
                ) : filteredCategories.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center py-4">
                      No categories found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredCategories.map((category) => (
                    <TableRow key={category.id}>
                      <TableCell className="font-medium">{category.name}</TableCell>
                      <TableCell>
                        {category.image_url ? (
                          <div className="relative h-12 w-12 rounded-md overflow-hidden">
                            <img
                              src={category.image_url}
                              alt={category.name}
                              className="h-full w-full object-cover"
                              onError={(e) => {
                                (e.target as HTMLImageElement).src = 'https://placehold.co/100x100?text=No+Image';
                              }}
                            />
                          </div>
                        ) : (
                          <div className="flex h-12 w-12 items-center justify-center rounded-md bg-muted">
                            <Image className="h-6 w-6 text-muted-foreground" />
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => openSubcategoriesDialog(category)}
                            title="Manage Subcategories"
                          >
                            <Layers className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => openEditDialog(category)}
                            title="Edit Category"
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDeleteCategory(category.id)}
                            title="Delete Category"
                          >
                            <Trash2 className="h-4 w-4 text-destructive" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Add Category Dialog */}
      <Dialog open={isAddingCategory} onOpenChange={setIsAddingCategory}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Category</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                placeholder="Enter category name"
                value={newCategory.name || ''}
                onChange={(e) => setNewCategory({ ...newCategory, name: e.target.value })}
              />
            </div>

            <ImageUpload
              label="Category Image"
              value={newCategory.image_url || null}
              onChange={handleCategoryImageChange}
              bucketName="product"
              folderPath="categories"
            />

            {categoryImageError && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <p className="text-sm text-red-600 flex items-center">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  {categoryImageError}
                </p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddingCategory(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddCategory}>Add Category</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Category Dialog */}
      <Dialog open={isEditingCategory} onOpenChange={setIsEditingCategory}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Category</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-name">Name</Label>
              <Input
                id="edit-name"
                placeholder="Enter category name"
                value={newCategory.name || ''}
                onChange={(e) => setNewCategory({ ...newCategory, name: e.target.value })}
              />
            </div>

            <ImageUpload
              label="Category Image"
              value={newCategory.image_url || null}
              onChange={handleCategoryImageChange}
              bucketName="product"
              folderPath="categories"
            />

            {categoryImageError && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <p className="text-sm text-red-600 flex items-center">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  {categoryImageError}
                </p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditingCategory(false)}>
              Cancel
            </Button>
            <Button onClick={handleEditCategory}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Subcategories Dialog */}
      <Dialog open={isViewingSubcategories} onOpenChange={setIsViewingSubcategories}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>
              {selectedCategory ? `Subcategories for ${selectedCategory.name}` : 'Subcategories'}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">Manage Subcategories</h3>
              <Button onClick={openAddSubcategoryDialog}>
                <Plus className="h-4 w-4 mr-2" />
                Add Subcategory
              </Button>
            </div>

            {error && (
              <div className="bg-destructive/15 text-destructive p-3 rounded-md mb-4">
                {error}
              </div>
            )}

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Image</TableHead>
                    <TableHead className="w-[100px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {subcategoriesLoading ? (
                    <TableRow>
                      <TableCell colSpan={3} className="text-center py-4">
                        Loading...
                      </TableCell>
                    </TableRow>
                  ) : subcategories.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={3} className="text-center py-4">
                        No subcategories found
                      </TableCell>
                    </TableRow>
                  ) : (
                    subcategories.map((subcategory) => (
                      <TableRow key={subcategory.id}>
                        <TableCell className="font-medium">{subcategory.name}</TableCell>
                        <TableCell>
                          {subcategory.image_url ? (
                            <div className="relative h-12 w-12 rounded-md overflow-hidden">
                              <img
                                src={subcategory.image_url}
                                alt={subcategory.name}
                                className="h-full w-full object-cover"
                                onError={(e) => {
                                  (e.target as HTMLImageElement).src = 'https://placehold.co/100x100?text=No+Image';
                                }}
                              />
                            </div>
                          ) : (
                            <div className="flex h-12 w-12 items-center justify-center rounded-md bg-muted">
                              <Image className="h-6 w-6 text-muted-foreground" />
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => openEditSubcategoryDialog(subcategory)}
                              title="Edit Subcategory"
                            >
                              <Pencil className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleDeleteSubcategory(subcategory.id, subcategory.category_id)}
                              title="Delete Subcategory"
                            >
                              <Trash2 className="h-4 w-4 text-destructive" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewingSubcategories(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Subcategory Dialog */}
      <Dialog open={isAddingSubcategory} onOpenChange={setIsAddingSubcategory}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Subcategory</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="subcategory-name">Name</Label>
              <Input
                id="subcategory-name"
                placeholder="Enter subcategory name"
                value={newSubcategory.name || ''}
                onChange={(e) => setNewSubcategory({ ...newSubcategory, name: e.target.value })}
              />
            </div>
            <ImageUpload
              label="Subcategory Image"
              value={newSubcategory.image_url || null}
              onChange={handleSubcategoryImageChange}
              bucketName="product"
              folderPath="subcategories"
            />

            {subcategoryImageError && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <p className="text-sm text-red-600 flex items-center">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  {subcategoryImageError}
                </p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddingSubcategory(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddSubcategory}>Add Subcategory</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Subcategory Dialog */}
      <Dialog open={isEditingSubcategory} onOpenChange={setIsEditingSubcategory}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Subcategory</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-subcategory-name">Name</Label>
              <Input
                id="edit-subcategory-name"
                placeholder="Enter subcategory name"
                value={newSubcategory.name || ''}
                onChange={(e) => setNewSubcategory({ ...newSubcategory, name: e.target.value })}
              />
            </div>
            <ImageUpload
              label="Subcategory Image"
              value={newSubcategory.image_url || null}
              onChange={handleSubcategoryImageChange}
              bucketName="product"
              folderPath="subcategories"
            />

            {subcategoryImageError && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <p className="text-sm text-red-600 flex items-center">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  {subcategoryImageError}
                </p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditingSubcategory(false)}>
              Cancel
            </Button>
            <Button onClick={handleEditSubcategory}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
