.rdp {
  --rdp-cell-size: 40px;
  --rdp-accent-color: hsl(var(--primary));
  --rdp-background-color: hsl(var(--primary) / 0.1);
  --rdp-accent-color-dark: hsl(var(--primary));
  --rdp-background-color-dark: hsl(var(--primary) / 0.2);
  --rdp-outline: 2px solid var(--rdp-accent-color);
  --rdp-outline-selected: 2px solid var(--rdp-accent-color);
  margin: 0;
}

.rdp-months {
  justify-content: center;
}

.rdp-month {
  background-color: hsl(var(--background));
  border-radius: 6px;
  padding: 16px;
  margin: 0;
}

.rdp-caption {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  text-align: left;
}

.rdp-caption_label {
  font-size: 16px;
  font-weight: 500;
}

.rdp-nav {
  display: flex;
  gap: 4px;
}

.rdp-head_cell {
  font-size: 12px;
  font-weight: 500;
  color: hsl(var(--muted-foreground));
  text-align: center;
  padding-bottom: 8px;
  width: var(--rdp-cell-size);
}

.rdp-cell {
  width: var(--rdp-cell-size);
  height: var(--rdp-cell-size);
  text-align: center;
}

.rdp-day {
  width: var(--rdp-cell-size);
  height: var(--rdp-cell-size);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 400;
  color: hsl(var(--foreground));
  transition: all 0.2s ease;
}

.rdp-day:hover {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

.rdp-day_selected {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.rdp-day_selected:hover {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.rdp-day_outside {
  opacity: 0.5;
}

.rdp-day_disabled {
  opacity: 0.5;
}

.rdp-button:hover:not([disabled]):not(.rdp-day_selected) {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}
