# Tech Context

**Project Name:** Dhanam Store

**Technologies Used:**

- React: A JavaScript library for building user interfaces.
- TypeScript: A superset of JavaScript that adds static typing.
- Vite: A build tool that provides fast development and optimized production builds.
- Tailwind CSS: A utility-first CSS framework for styling the application.
- Radix UI: A set of accessible and customizable UI components.
- clsx: A utility for conditionally joining class names.
- tailwind-merge: A utility for merging Tailwind CSS classes.
- lucide-react: A library of icons.
- zod: A TypeScript-first schema validation with static type inference.
- date-fns: Modern JavaScript date utility library.
- recharts: A composable charting library built on React components
- Supabase: An open-source Firebase alternative.
- @supabase/supabase-js: A JavaScript client library for Supabase.

**Development Setup:**

- The project is set up with Vite, React, and TypeScript.
- The dependencies are managed using npm.
- The project uses ESLint for linting and code formatting.

**Technical Constraints:**

- The application must be responsive and accessible.
- The application must be fast and reliable.
- The application must be maintainable and scalable.

**Dependencies:**

- "@hello-pangea/dnd": "^16.5.0",
- "@hookform/resolvers": "^3.9.0",
- "@radix-ui/react-accordion": "^1.2.0",
- "@radix-ui/react-alert-dialog": "^1.1.1",
- "@radix-ui/react-aspect-ratio": "^1.1.0",
- "@radix-ui/react-avatar": "^1.1.0",
- "@radix-ui/react-checkbox": "^1.1.1",
- "@radix-ui/react-collapsible": "^1.1.0",
- "@radix-ui/react-context-menu": "^2.2.1",
- "@radix-ui/react-dialog": "^1.1.1",
- "@radix-ui/react-dropdown-menu": "^2.1.1",
- "@radix-ui/react-hover-card": "^1.1.1",
- "@radix-ui/react-icons": "^1.3.0",
- "@radix-ui/react-label": "^2.1.0",
- "@radix-ui/react-menubar": "^1.1.1",
- "@radix-ui/react-navigation-menu": "^1.2.0",
- "@radix-ui/react-popover": "^1.1.1",
- "@radix-ui/react-progress": "^1.1.0",
- "@radix-ui/react-radio-group": "^1.2.0",
- "@radix-ui/react-scroll-area": "^1.1.0",
- "@radix-ui/react-select": "^2.1.1",
- "@radix-ui/react-separator": "^1.1.0",
- "@radix-ui/react-slider": "^1.2.0",
- "@radix-ui/react-slot": "^1.1.0",
- "@radix-ui/react-switch": "^1.1.0",
- "@radix-ui/react-tabs": "^1.1.0",
- "@radix-ui/react-toast": "^1.2.1",
- "@radix-ui/react-toggle": "^1.1.0",
- "@radix-ui/react-toggle-group": "^1.1.0",
- "@radix-ui/react-tooltip": "^1.1.2",
- "class-variance-authority": "^0.7.0",
- "clsx": "^2.1.1",
- "cmdk": "^1.0.0",
- "date-fns": "^3.6.0",
- "embla-carousel-react": "^8.3.0",
- "input-otp": "^1.2.4",
- "lucide-react": "^0.446.0",
- "next-themes": "^0.3.0",
- "react": "^18.3.1",
- "react-dom": "^18.3.1",
- "react-hook-form": "^7.53.0",
- "react-resizable-panels": "^2.1.3",
- "recharts": "^2.12.7",
- "sonner": "^1.5.0",
- "tailwind-merge": "^2.5.2",
- "tailwindcss-animate": "^1.0.7",
- "vaul": "^1.0.0",
- "zod": "^3.23.8",
- "@supabase/supabase-js": "^2.1.0"
