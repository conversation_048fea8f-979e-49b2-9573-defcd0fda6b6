import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Upload, Image as ImageIcon, X } from 'lucide-react';
import { supabase } from '@/lib/supabase.config';

interface ImageUploadProps {
  value: string | null;
  onChange: (url: string | null) => void;
  label?: string;
  bucketName?: string;
  folderPath?: string;
}

export function ImageUpload({
  value,
  onChange,
  label = 'Image',
  bucketName = 'product',
  folderPath = '',
}: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [urlInput, setUrlInput] = useState(value || '');
  const [showUrlInput, setShowUrlInput] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setUploadError('Please select an image file');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setUploadError('Image size should be less than 5MB');
      return;
    }

    setIsUploading(true);
    setUploadError(null);

    try {
      // Generate a unique file name
      const fileExt = file.name.split('.').pop();
      const fileName = `${Math.random().toString(36).substring(2, 15)}_${Date.now()}.${fileExt}`;
      const filePath = folderPath ? `${folderPath}/${fileName}` : fileName;

      console.log(`Attempting direct upload to bucket: ${bucketName}, path: ${filePath}`);
      
      // Get current auth session
      const { data: { session } } = await supabase.auth.getSession();
      console.log("Auth session exists:", !!session);
      
      // Force refresh auth session if it exists
      if (session) {
        await supabase.auth.refreshSession();
      }
      
      // Direct upload approach
      const { data, error: uploadError } = await supabase.storage
        .from(bucketName)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: true,
          contentType: file.type
        });

      if (uploadError) {
        console.error('Upload error details:', uploadError);
        throw new Error(`Upload failed: ${uploadError.message}`);
      }

      console.log('Upload successful:', data);

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from(bucketName)
        .getPublicUrl(filePath);

      console.log('Public URL:', publicUrl);
      onChange(publicUrl);
    } catch (error: any) {
      console.error('Error uploading image:', error);
      setUploadError(`Error: ${error.message || 'Unknown upload error'}`);
    } finally {
      setIsUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleUrlSubmit = () => {
    onChange(urlInput || null);
    setShowUrlInput(false);
  };

  const handleRemoveImage = () => {
    onChange(null);
    setUrlInput('');
  };

  return (
    <div className="space-y-2">
      <Label>{label}</Label>

      {value ? (
        <div className="relative rounded-md border overflow-hidden h-48">
          <img
            src={value}
            alt="Uploaded"
            className="w-full h-full object-cover"
            onError={() => setUploadError('Failed to load image')}
          />
          <Button
            variant="destructive"
            size="icon"
            className="absolute top-2 right-2 rounded-full"
            onClick={handleRemoveImage}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      ) : (
        <div className="grid gap-2">
          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              className="w-full"
              onClick={() => fileInputRef.current?.click()}
              disabled={isUploading}
            >
              <Upload className="h-4 w-4 mr-2" />
              {isUploading ? 'Uploading...' : 'Upload Image'}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowUrlInput(!showUrlInput)}
            >
              <ImageIcon className="h-4 w-4 mr-2" />
              Image URL
            </Button>
          </div>
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            accept="image/*"
            className="hidden"
          />
        </div>
      )}

      {showUrlInput && (
        <div className="flex gap-2 mt-2">
          <Input
            placeholder="Enter image URL"
            value={urlInput}
            onChange={(e) => setUrlInput(e.target.value)}
          />
          <Button type="button" onClick={handleUrlSubmit}>
            Save
          </Button>
        </div>
      )}

      {uploadError && (
        <div className="mt-2">
          <p className="text-sm text-destructive mb-2">{uploadError}</p>
          {!showUrlInput && (
            <Button
              type="button"
              variant="secondary"
              size="sm"
              onClick={() => setShowUrlInput(true)}
            >
              Use Image URL Instead
            </Button>
          )}
        </div>
      )}
    </div>
  );
}
